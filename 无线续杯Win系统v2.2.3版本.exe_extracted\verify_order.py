#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import re
import requests
import threading
import verify_config
import platform
from PyQt6.QtWidgets import (
    QApplication, QDialog, QLabel, QLineEdit, QPushButton, 
    QVBoxLayout, QHBoxLayout, QMessageBox
)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QIcon, QFont, QColor, QPalette, QIntValidator

# 导入用于管理员权限检查的模块
import sys
if platform.system() == 'Windows':
    import ctypes
    from ctypes import windll

# 获取资源目录的辅助函数
def get_resource_path(relative_path):
    """获取资源绝对路径，兼容开发环境和PyInstaller打包后的环境"""
    try:
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS中
        if getattr(sys, '_MEIPASS', None):
            base_path = sys._MEIPASS
        else:
            # 在开发环境中，使用当前工作目录
            base_path = os.getcwd()
            
        return os.path.join(base_path, relative_path)
    except Exception:
        # 如果获取失败，回退到相对路径
        return os.path.join(os.getcwd(), relative_path)

# 函数检查是否以管理员权限运行
def is_admin():
    """检查程序是否以管理员权限运行"""
    if platform.system() == 'Windows':
        try:
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False
    # 非Windows系统返回True，避免改变行为
    return True

# 函数请求管理员权限并重启程序
def run_as_admin():
    """请求管理员权限并重启程序（仅Windows系统）"""
    if platform.system() != 'Windows':
        return False
        
    try:
        args = [sys.executable] + sys.argv
        
        # 通过ShellExecute请求提升权限
        print("请求管理员权限...")
        ctypes.windll.shell32.ShellExecuteW(None, "runas", args[0], " ".join('"' + arg + '"' for arg in args[1:]), None, 1)
        return True
    except Exception as e:
        print(f"获取管理员权限失败: {e}")
        return False

# 函数检查并请求管理员权限
def check_and_request_admin():
    """检查并请求管理员权限（如果需要）"""
    # 检查是否为打包的可执行文件
    is_frozen = getattr(sys, 'frozen', False)
    
    # 如果是Windows系统下的打包可执行文件且没有管理员权限
    if platform.system() == 'Windows' and is_frozen and not is_admin():
        print("为了正确访问系统文件，需要管理员权限运行")
        # 请求管理员权限并退出当前实例
        if run_as_admin():
            sys.exit(0)
        else:
            print("无法获取管理员权限，部分功能可能无法正常使用")
            # 显示警告消息框
            if QApplication.instance():
                QMessageBox.warning(None, "权限不足", 
                                   "程序无法获取管理员权限，访问C盘文件可能失败。\n"
                                   "请右键以管理员身份运行此程序。")

def create_directory_if_not_exists(directory_path):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory_path):
        try:
            os.makedirs(directory_path)
            print(f"Created directory: {directory_path}")
        except Exception as e:
            print(f"Failed to create directory: {e}")
            return False
    return True

def is_valid_order_number(order_number):
    """验证订单号格式是否正确"""
    pattern = r'^\d{19}$'
    return bool(re.match(pattern, order_number))

def write_to_blacklist1(order_number):
    """将订单号写入C盘根目录的blacklist1.json"""
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    
    # 确保目录存在
    if not create_directory_if_not_exists(d_drive):
        print("Unable to access drive, please make sure it exists and has write permissions")
        return False
    
    blacklist1_path = os.path.join(d_drive, "blacklist1.json")
    
    # 获取当前时间作为注册时间
    current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    
    # 创建数据 - 简化格式，只存储订单号
    data = {
        "order_number": order_number
    }
    
    try:
        with open(blacklist1_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"Order number saved to {blacklist1_path}")
        return True
    except Exception as e:
        print(f"Failed to save order number: {e}")
        return False

def create_empty_blacklist2():
    """创建空的blacklist2.json文件"""
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    
    # 确保目录存在
    if not create_directory_if_not_exists(d_drive):
        print("Unable to access drive, please make sure it exists and has write permissions")
        return False
    
    blacklist2_path = os.path.join(d_drive, "blacklist2.json")
    
    try:
        # 如果文件不存在，创建一个空的JSON文件 - 使用数组格式，方便后期添加多个订单号
        if not os.path.exists(blacklist2_path):
            with open(blacklist2_path, 'w', encoding='utf-8') as f:
                f.write("[]")
            print(f"Created empty file {blacklist2_path}")
        return True
    except Exception as e:
        print(f"Failed to create empty file: {e}")
        return False

def download_blacklist2_from_gitee():
    """从Gitee仓库下载最新的blacklist2.json文件"""
    # 从配置获取Gitee URL
    urls = verify_config.get_gitee_urls()
    gitee_url = urls["api_url"]
    
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    blacklist2_path = os.path.join(d_drive, "blacklist2.json")
    
    try:
        # 获取文件信息
        response = requests.get(gitee_url)
        if response.status_code == 200:
            file_info = response.json()
            # 获取文件内容（Base64编码）
            import base64
            content = base64.b64decode(file_info['content']).decode('utf-8')
            
            # 尝试解析JSON内容
            try:
                downloaded_data = json.loads(content)
                
                # 确保格式为数组
                if not isinstance(downloaded_data, list):
                    # 如果是对象并且有orders字段
                    if isinstance(downloaded_data, dict) and "orders" in downloaded_data:
                        data_to_save = downloaded_data["orders"]
                    # 如果是对象有单个订单号
                    elif isinstance(downloaded_data, dict) and "order_number" in downloaded_data:
                        data_to_save = [downloaded_data["order_number"]]
                    # 其他情况，创建空数组
                    else:
                        data_to_save = []
                else:
                    # 如果已经是数组，检查并清洗内容
                    clean_data = []
                    for item in downloaded_data:
                        if isinstance(item, str):
                            clean_data.append(item)
                        elif isinstance(item, dict) and "order_number" in item:
                            clean_data.append(item["order_number"])
                    data_to_save = clean_data
                
                # 保存为标准数组格式
                with open(blacklist2_path, 'w', encoding='utf-8') as f:
                    json.dump(data_to_save, f, ensure_ascii=False, indent=2)
                
                print("Successfully updated blacklist file from Gitee API")
                print(f"Blacklist contains {len(data_to_save)} orders")
                return True
            except json.JSONDecodeError:
                # 如果不是有效的JSON，创建空数组
                with open(blacklist2_path, 'w', encoding='utf-8') as f:
                    f.write("[]")
                print("Downloaded file is not valid JSON format, created empty blacklist file")
                return True
        else:
            print(f"Failed to get file from Gitee: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading blacklist file: {e}")
        return False

def download_domain_from_gitee():
    """从Gitee仓库下载最新的domain.json文件"""
    # 从配置获取Gitee URL
    urls = verify_config.get_domain_urls()
    gitee_url = urls["api_url"]
    
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    domain_path = os.path.join(d_drive, "domain.json")
    
    try:
        # 获取文件信息
        response = requests.get(gitee_url)
        if response.status_code == 200:
            file_info = response.json()
            # 获取文件内容（Base64编码）
            import base64
            content = base64.b64decode(file_info['content']).decode('utf-8')
            
            # 尝试解析JSON内容
            try:
                downloaded_data = json.loads(content)
                
                # 保存文件
                with open(domain_path, 'w', encoding='utf-8') as f:
                    json.dump(downloaded_data, f, ensure_ascii=False, indent=2)
                
                print("成功更新domain.json文件")
                return True
            except json.JSONDecodeError:
                print("下载的domain.json文件不是有效的JSON格式")
                return False
        else:
            print(f"从Gitee获取domain.json失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"下载domain.json文件时出错: {e}")
        return False

def download_domain_from_raw_url():
    """从Gitee原始URL下载domain.json文件"""
    # 从配置获取原始URL
    urls = verify_config.get_domain_urls()
    raw_url = urls["raw_url"]
    
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    domain_path = os.path.join(d_drive, "domain.json")
    
    try:
        # 下载文件
        response = requests.get(raw_url)
        if response.status_code == 200:
            # 确保目录存在
            if not create_directory_if_not_exists(d_drive):
                print("无法访问驱动器，请确保它存在并有写入权限")
                return False
            
            # 尝试解析JSON内容
            try:
                content = response.text
                downloaded_data = json.loads(content)
                
                # 保存文件
                with open(domain_path, 'w', encoding='utf-8') as f:
                    json.dump(downloaded_data, f, ensure_ascii=False, indent=2)
                
                print(f"成功从原始URL更新domain.json文件")
                return True
            except json.JSONDecodeError:
                print("下载的domain.json文件不是有效的JSON格式")
                return False
        else:
            print(f"从Gitee原始URL获取domain.json失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"下载domain.json文件时出错: {e}")
        return False

def update_domain_background():
    """在后台线程中更新domain.json文件"""
    # 创建并启动后台线程
    update_thread = threading.Thread(target=update_domain_task)
    update_thread.daemon = True  # 设置为守护线程，这样主程序退出时线程也会结束
    update_thread.start()

def update_domain_task():
    """尝试多种方式更新domain.json文件的任务"""
    print("开始在后台更新domain.json文件...")
    
    # 尝试从Gitee API下载
    if download_domain_from_gitee():
        return
    
    # 如果API下载失败，尝试从原始URL下载
    if download_domain_from_raw_url():
        return
    
    print("所有尝试都失败，无法更新domain.json文件")

def update_blacklist2_background():
    """在后台线程中更新blacklist2.json文件"""
    # 移除检查自动更新设置的代码，始终执行更新
    # if not verify_config.is_auto_update_enabled():
    #     print("Auto-update is disabled")
    #     return
    
    def update_task():
        try:
            print("Starting to update blacklist file from Gitee...")
            
            # 首先尝试直接下载方式，最简单可靠
            if download_blacklist2_direct():
                return
                
            # 然后尝试使用git clone方式下载
            if download_blacklist2_from_git():
                return
                
            # 如果git clone失败，尝试从原始URL下载
            if download_blacklist2_from_raw_url():
                return
            
            # 如果上面都失败，尝试使用API下载
            if download_blacklist2_from_gitee():
                return
            
            # 如果都失败，确保至少有一个空的blacklist2文件
            print("Failed to update from Gitee, using local file")
            create_empty_blacklist2()
        except Exception as e:
            print(f"Error updating blacklist file: {e}")
            create_empty_blacklist2()
    
    # 创建后台线程执行更新任务
    update_thread = threading.Thread(target=update_task)
    update_thread.daemon = True  # 设置为守护线程，这样主程序退出时线程也会结束
    update_thread.start()

def download_blacklist2_from_raw_url():
    """从Gitee原始URL下载blacklist2.json文件"""
    # 从配置获取原始URL
    urls = verify_config.get_gitee_urls()
    raw_url = urls["raw_url"]
    
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    blacklist2_path = os.path.join(d_drive, "blacklist2.json")
    
    try:
        # 下载文件
        response = requests.get(raw_url)
        if response.status_code == 200:
            # 确保目录存在
            if not create_directory_if_not_exists(d_drive):
                print("Unable to access drive, please make sure it exists and has write permissions")
                return False
            
            # 尝试解析JSON内容
            try:
                content = response.text
                downloaded_data = json.loads(content)
                
                # 确保格式为数组
                if not isinstance(downloaded_data, list):
                    # 如果是对象并且有orders字段
                    if isinstance(downloaded_data, dict) and "orders" in downloaded_data:
                        data_to_save = downloaded_data["orders"]
                    # 如果是对象有单个订单号
                    elif isinstance(downloaded_data, dict) and "order_number" in downloaded_data:
                        data_to_save = [downloaded_data["order_number"]]
                    # 其他情况，创建空数组
                    else:
                        data_to_save = []
                else:
                    # 如果已经是数组，检查并清洗内容
                    clean_data = []
                    for item in downloaded_data:
                        if isinstance(item, str):
                            clean_data.append(item)
                        elif isinstance(item, dict) and "order_number" in item:
                            clean_data.append(item["order_number"])
                    data_to_save = clean_data
                
                # 保存为标准数组格式
                with open(blacklist2_path, 'w', encoding='utf-8') as f:
                    json.dump(data_to_save, f, ensure_ascii=False, indent=2)
                
                print("Successfully updated blacklist file from raw URL")
                print(f"Blacklist contains {len(data_to_save)} orders")
                return True
            except json.JSONDecodeError:
                # 如果不是有效的JSON，创建空数组
                with open(blacklist2_path, 'w', encoding='utf-8') as f:
                    f.write("[]")
                print("Downloaded file is not valid JSON format, created empty blacklist file")
                return True
        else:
            print(f"Failed to get file from raw URL: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading blacklist file: {e}")
        return False

def download_blacklist2_from_git():
    """使用git命令从Gitee仓库克隆并获取blacklist2.json文件"""
    import tempfile
    import shutil
    import subprocess
    
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    blacklist2_path = os.path.join(d_drive, "blacklist2.json")
    
    # 创建临时目录用于git操作
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 从配置获取仓库URL
        repo_url = f"https://gitee.com/{verify_config.GITEE_USERNAME}/{verify_config.REPO_NAME}.git"
        
        print(f"Cloning repository from {repo_url} to {temp_dir}...")
        
        # 执行git clone
        result = subprocess.run(
            ["git", "clone", repo_url, temp_dir],
            check=True,
            capture_output=True,
            text=True
        )
        
        # 找到临时目录中的blacklist2.json文件
        source_file = os.path.join(temp_dir, "blacklist2.json")
        
        if os.path.exists(source_file):
            # 读取文件内容并解析
            try:
                with open(source_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    downloaded_data = json.loads(content)
                
                # 确保格式为数组
                if not isinstance(downloaded_data, list):
                    # 如果是对象并且有orders字段
                    if isinstance(downloaded_data, dict) and "orders" in downloaded_data:
                        data_to_save = downloaded_data["orders"]
                    # 如果是对象有单个订单号
                    elif isinstance(downloaded_data, dict) and "order_number" in downloaded_data:
                        data_to_save = [downloaded_data["order_number"]]
                    # 其他情况，创建空数组
                    else:
                        data_to_save = []
                else:
                    # 如果已经是数组，检查并清洗内容
                    clean_data = []
                    for item in downloaded_data:
                        if isinstance(item, str):
                            clean_data.append(item)
                        elif isinstance(item, dict) and "order_number" in item:
                            clean_data.append(item["order_number"])
                    data_to_save = clean_data
                
                # 保存为标准数组格式
                with open(blacklist2_path, 'w', encoding='utf-8') as f:
                    json.dump(data_to_save, f, ensure_ascii=False, indent=2)
                
                print(f"Successfully updated blacklist file from Git repository to: {blacklist2_path}")
                print(f"Blacklist contains {len(data_to_save)} orders")
                return True
            except json.JSONDecodeError:
                # 如果不是有效的JSON，创建空数组
                with open(blacklist2_path, 'w', encoding='utf-8') as f:
                    f.write("[]")
                print("Cloned file is not valid JSON format, created empty blacklist file")
                return True
        else:
            print(f"blacklist2.json file not found in the cloned repository")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"Git clone failed: {e.stderr}")
        return False
    except Exception as e:
        print(f"Error downloading blacklist file: {e}")
        return False
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

def download_blacklist2_direct():
    """直接从Gitee下载仓库文件，不依赖git命令"""
    import requests
    import platform
    
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    blacklist2_path = os.path.join(d_drive, "blacklist2.json")
    
    # 直接使用仓库原始文件URL
    direct_url = "https://gitee.com/flzt_1/blacklist2.json/raw/master/blacklist2.json"
    
    try:
        print(f"Downloading file directly from Gitee: {direct_url}")
        
        # 下载文件
        response = requests.get(direct_url, timeout=15)
        
        if response.status_code == 200:
            # 确保C盘目录存在
            if not create_directory_if_not_exists(d_drive):
                print("Unable to access C drive, please make sure it exists and has write permissions")
                return False
            
            # 尝试解析下载的内容，确保是有效的JSON
            content = response.text
            try:
                downloaded_data = json.loads(content)
                
                # 如果下载的不是数组格式，转换为数组格式
                if not isinstance(downloaded_data, list):
                    # 如果是对象并且有orders字段
                    if isinstance(downloaded_data, dict) and "orders" in downloaded_data:
                        data_to_save = downloaded_data["orders"]
                    # 如果是对象有单个订单号
                    elif isinstance(downloaded_data, dict) and "order_number" in downloaded_data:
                        data_to_save = [downloaded_data["order_number"]]
                    # 其他情况，创建空数组
                    else:
                        data_to_save = []
                else:
                    # 如果已经是数组，检查内容格式
                    clean_data = []
                    for item in downloaded_data:
                        if isinstance(item, str):
                            clean_data.append(item)
                        elif isinstance(item, dict) and "order_number" in item:
                            clean_data.append(item["order_number"])
                    data_to_save = clean_data
                
                # 保存为标准数组格式
                with open(blacklist2_path, 'w', encoding='utf-8') as f:
                    json.dump(data_to_save, f, ensure_ascii=False, indent=2)
                    
                print(f"Successfully downloaded blacklist file directly from Gitee to: {blacklist2_path}")
                print(f"Blacklist contains {len(data_to_save)} orders")
                return True
            except json.JSONDecodeError:
                # 如果不是有效的JSON，创建一个空数组
                with open(blacklist2_path, 'w', encoding='utf-8') as f:
                    f.write("[]")
                print("Downloaded file is not valid JSON format, created empty blacklist file")
                return True
        else:
            print(f"Failed to download file, HTTP status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"Error downloading file directly: {e}")
        return False

def verify_order_number():
    """验证订单号是否已在blacklist2.json中"""
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    blacklist1_path = os.path.join(d_drive, "blacklist1.json")
    blacklist2_path = os.path.join(d_drive, "blacklist2.json")
    
    # 检查blacklist1.json是否存在
    if not os.path.exists(blacklist1_path):
        print("Order record file not found, please enter order number first")
        return False
    
    # 检查blacklist2.json是否存在
    if not os.path.exists(blacklist2_path):
        create_empty_blacklist2()
    
    try:
        # 读取blacklist1.json获取当前订单号
        with open(blacklist1_path, 'r', encoding='utf-8') as f:
            blacklist1_data = json.load(f)
            current_order_number = blacklist1_data.get("order_number", "")
        
        # 读取blacklist2.json检查是否已存在该订单号
        with open(blacklist2_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content or content == "[]":  # 如果文件为空或是空数组
                blacklist2_data = []
            else:
                try:
                    blacklist2_data = json.loads(content)
                except json.JSONDecodeError:
                    blacklist2_data = []
        
        # 简化的检查逻辑，只需判断订单号是否在列表中
        if isinstance(blacklist2_data, list):
            # 数组格式 - 直接检查订单号是否在数组中
            if current_order_number in blacklist2_data:
                print(f"Order number {current_order_number} has already been used")
                return False
        elif isinstance(blacklist2_data, dict):
            # 可能是旧格式，检查是否有orders字段
            if "orders" in blacklist2_data and isinstance(blacklist2_data["orders"], list):
                if current_order_number in blacklist2_data["orders"]:
                    print(f"Order number {current_order_number} has already been used")
                    return False
            # 单个订单号格式
            elif blacklist2_data.get("order_number") == current_order_number:
                print(f"Order number {current_order_number} has already been used")
                return False
        
        return True
    except Exception as e:
        print(f"Error verifying order number: {e}")
        return False

# 订单验证对话框
class OrderVerificationDialog(QDialog):
    """订单验证对话框，风格与主应用验证码输入对话框一致"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("订单验证")
        self.setMinimumWidth(440)
        self.setFixedHeight(300)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)
        
        # 设置窗口图标，优先使用ico文件
        icon_path = get_resource_path(os.path.join("icons", "LOGO.ico"))
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        else:
            # 后备方案，使用PNG图标
            icon_path = get_resource_path(os.path.join("images", "LOGO.png"))
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(15)
        
        # 添加图标和标题
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(15)
        
        # 创建验证图标
        icon_label = QLabel()
        icon_label.setFixedSize(38, 38)
        icon_label.setStyleSheet("""
            background-color: #152030;
            border-radius: 19px;
            color: #ffffff;
            font-size: 18px;
            font-weight: bold;
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setText("￥")  # 星形图标
        
        # 创建标题标签
        title_label = QLabel("订单验证")
        title_label.setStyleSheet("""
            color: #ffffff;
            font-size: 18px;
            font-weight: bold;
            font-family: 'Segoe UI', '微软雅黑';
        """)
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch(1)
        
        layout.addLayout(header_layout)
        
        # 添加说明文本
        info_label = QLabel("请输入您的订单编号以继续")
        info_label.setStyleSheet("""
            color: #cccccc;
            font-size: 14px;
            font-family: 'Segoe UI', '微软雅黑';
            margin-top: 5px;
            margin-bottom: 10px;
        """)
        layout.addWidget(info_label)
        
        # 添加输入框
        self.order_input = QLineEdit()
        self.order_input.setPlaceholderText("订单编号")
        # 移除最大长度限制，允许用户输入任意长度的文本
        # self.order_input.setMaxLength(19)
        self.order_input.setFixedHeight(48)
        self.order_input.setStyleSheet("""
            QLineEdit {
                background-color: #101520;
                color: #ffffff;
                border: 2px solid #1e2430;
                border-radius: 8px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', '微软雅黑';
                letter-spacing: 2px;
            }
            
            QLineEdit:focus {
                border: 2px solid #0078d7;
            }
        """)
        
        # 使用textChanged信号连接到validate_input方法进行实时验证
        self.order_input.textChanged.connect(self.validate_input)
        
        layout.addWidget(self.order_input)
        
        # 添加错误提示标签
        self.error_label = QLabel("")
        self.error_label.setStyleSheet("""
            color: #f44336;
            font-size: 13px;
            font-family: 'Segoe UI', '微软雅黑';
            margin-top: 5px;
            min-height: 20px;
        """)
        layout.addWidget(self.error_label)
        
        layout.addStretch(1)
        
        # 添加按钮
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)
        
        cancel_button = QPushButton("退出")
        cancel_button.setFixedHeight(42)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #101520;
                color: #cccccc;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Segoe UI', '微软雅黑';
                min-width: 100px;
            }
            
            QPushButton:hover {
                background-color: #1e2430;
                color: #ffffff;
            }
            
            QPushButton:pressed {
                background-color: #0d121a;
            }
        """)
        
        confirm_button = QPushButton("验证")
        confirm_button.setFixedHeight(42)
        confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d7;
                color: #ffffff;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Segoe UI', '微软雅黑';
                min-width: 100px;
            }
            
            QPushButton:hover {
                background-color: #1084d9;
            }
            
            QPushButton:pressed {
                background-color: #0067c0;
            }
        """)
        
        button_layout.addStretch(1)
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(confirm_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号和槽
        cancel_button.clicked.connect(self.handle_cancel)
        confirm_button.clicked.connect(self.verify_order)
        self.order_input.returnPressed.connect(self.verify_order)
        
        # 设置焦点
        self.order_input.setFocus()
        
        # 添加动画
        self.animation = QPropertyAnimation(self, b"windowOpacity")
        self.animation.setDuration(300)
        self.animation.setStartValue(0.0)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.animation.start()
        
        # 设置整体样式
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                border-radius: 10px;
            }
        """)
    
    def validate_input(self, text):
        """验证输入的文本是否符合数字要求"""
        if text:
            # 检查是否都是数字
            if not text.isdigit():
                self.error_label.setText("订单号只能包含数字")
                self.order_input.setStyleSheet("""
                    QLineEdit {
                        background-color: #101520;
                        color: #ffffff;
                        border: 2px solid #f44336;
                        border-radius: 8px;
                        padding: 8px 15px;
                        font-size: 16px;
                        font-weight: bold;
                        font-family: 'Segoe UI', '微软雅黑';
                        letter-spacing: 2px;
                    }
                """)
            else:
                # 不论长度如何，都不显示有关19位数字的提示
                # 长度不对时也保持普通样式，不使用警告色
                    self.error_label.setText("")
                    self.order_input.setStyleSheet("""
                        QLineEdit {
                            background-color: #101520;
                            color: #ffffff;
                            border: 2px solid #1e2430;
                            border-radius: 8px;
                            padding: 8px 15px;
                            font-size: 16px;
                            font-weight: bold;
                            font-family: 'Segoe UI', '微软雅黑';
                            letter-spacing: 2px;
                        }
                        
                        QLineEdit:focus {
                            border: 2px solid #0078d7;
                        }
                    """)
        else:
            # 输入为空
            self.error_label.setText("")
            self.order_input.setStyleSheet("""
                QLineEdit {
                    background-color: #101520;
                    color: #ffffff;
                    border: 2px solid #1e2430;
                    border-radius: 8px;
                    padding: 8px 15px;
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', '微软雅黑';
                    letter-spacing: 2px;
                }
                
                QLineEdit:focus {
                    border: 2px solid #0078d7;
                }
            """)
    
    def verify_order(self):
        """验证订单号"""
        # 获取输入的订单号
        full_input = self.order_input.text().strip()
        
        # 验证订单号
        if not full_input:
            self.error_label.setText("请输入订单号")
            self.order_input.setStyleSheet("""
                QLineEdit {
                    background-color: #101520;
                    color: #ffffff;
                    border: 2px solid #f44336;
                    border-radius: 8px;
                    padding: 8px 15px;
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', '微软雅黑';
                    letter-spacing: 2px;
                }
            """)
            return
            
        # 检查长度是否为19位，不是则视为无效
        if len(full_input) != 19:
            # 不提示具体需要19位数字，只提示订单号无效
            self.error_label.setText("订单号无效")
            self.order_input.setStyleSheet("""
                QLineEdit {
                    background-color: #101520;
                    color: #ffffff;
                    border: 2px solid #f44336;
                    border-radius: 8px;
                    padding: 8px 15px;
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', '微软雅黑';
                    letter-spacing: 2px;
                }
            """)
            return
            
        # 检查是否为有效的19位数字格式
        if not is_valid_order_number(full_input):
            self.error_label.setText("订单号格式不正确")
            self.order_input.setStyleSheet("""
                QLineEdit {
                    background-color: #101520;
                    color: #ffffff;
                    border: 2px solid #f44336;
                    border-radius: 8px;
                    padding: 8px 15px;
                    font-size: 16px;
                    font-weight: bold;
                    font-family: 'Segoe UI', '微软雅黑';
                    letter-spacing: 2px;
                }
            """)
            return
        
        # 保存订单号
        if write_to_blacklist1(full_input):
            QMessageBox.information(self, "Cusor无线续杯", "欢迎您使用！")
            self.accept()  # 接受对话框，表示验证成功
        else:
            self.error_label.setText("验证失败，请检查磁盘权限")
    
    def handle_cancel(self):
        """处理取消按钮"""
        result = QMessageBox.question(
            self,
            "确认退出",
            "您尚未完成订单验证，确定要退出程序吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if result == QMessageBox.StandardButton.Yes:
            self.reject()  # 拒绝对话框，表示验证失败
            
    def get_order_number(self):
        """返回输入的订单号"""
        # 返回完整输入，不再截取
        return self.order_input.text().strip()

# 验证成功对话框
class VerificationSuccessDialog(QDialog):
    """验证成功对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("验证成功")
        self.setMinimumWidth(360)
        self.setFixedHeight(220)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)
        
        # 设置窗口图标，优先使用ico文件
        icon_path = get_resource_path(os.path.join("icons", "LOGO.ico"))
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        else:
            # 后备方案，使用PNG图标
            icon_path = get_resource_path(os.path.join("images", "LOGO.png"))
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(15)
        
        # 添加图标和标题
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(15)
        
        # 创建验证图标
        icon_label = QLabel()
        icon_label.setFixedSize(38, 38)
        icon_label.setStyleSheet("""
            background-color: #4CAF50;
            border-radius: 19px;
            color: #ffffff;
            font-size: 18px;
            font-weight: bold;
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setText("✓")  # 对勾图标
        
        # 创建标题标签
        title_label = QLabel("验证成功")
        title_label.setStyleSheet("""
            color: #ffffff;
            font-size: 18px;
            font-weight: bold;
            font-family: 'Segoe UI', '微软雅黑';
        """)
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch(1)
        
        layout.addLayout(header_layout)
        
        # 添加说明文本
        info_label = QLabel("欢迎使用！")
        info_label.setStyleSheet("""
            color: #cccccc;
            font-size: 14px;
            font-family: 'Segoe UI', '微软雅黑';
            margin-top: 5px;
            margin-bottom: 10px;
        """)
        layout.addWidget(info_label)
        
        layout.addStretch(1)
        
        # 添加按钮
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)
        
        confirm_button = QPushButton("确定")
        confirm_button.setFixedHeight(42)
        confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: #ffffff;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Segoe UI', '微软雅黑';
                min-width: 100px;
            }
            
            QPushButton:hover {
                background-color: #5cb860;
            }
            
            QPushButton:pressed {
                background-color: #3b9a3f;
            }
        """)
        
        button_layout.addStretch(1)
        button_layout.addWidget(confirm_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号和槽
        confirm_button.clicked.connect(self.accept)
        
        # 添加动画
        self.animation = QPropertyAnimation(self, b"windowOpacity")
        self.animation.setDuration(300)
        self.animation.setStartValue(0.0)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.animation.start()
        
        # 设置整体样式
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                border-radius: 10px;
            }
        """)

# 验证失败对话框
class VerificationFailedDialog(QDialog):
    """验证失败对话框"""
    
    def __init__(self, reason="订单号无效", parent=None):
        super().__init__(parent)
        self.setWindowTitle("验证失败")
        self.setMinimumWidth(360)
        self.setFixedHeight(220)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowType.WindowContextHelpButtonHint)
        
        # 设置窗口图标，优先使用ico文件
        icon_path = get_resource_path(os.path.join("icons", "LOGO.ico"))
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        else:
            # 后备方案，使用PNG图标
            icon_path = get_resource_path(os.path.join("images", "LOGO.png"))
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(15)
        
        # 添加图标和标题
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(15)
        
        # 创建验证图标
        icon_label = QLabel()
        icon_label.setFixedSize(38, 38)
        icon_label.setStyleSheet("""
            background-color: #F44336;
            border-radius: 19px;
            color: #ffffff;
            font-size: 18px;
            font-weight: bold;
        """)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setText("×")  # 错误图标
        
        # 创建标题标签
        title_label = QLabel("验证失败")
        title_label.setStyleSheet("""
            color: #ffffff;
            font-size: 18px;
            font-weight: bold;
            font-family: 'Segoe UI', '微软雅黑';
        """)
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch(1)
        
        layout.addLayout(header_layout)
        
        # 添加说明文本
        info_label = QLabel(reason)
        info_label.setStyleSheet("""
            color: #cccccc;
            font-size: 14px;
            font-family: 'Segoe UI', '微软雅黑';
            margin-top: 5px;
            margin-bottom: 10px;
        """)
        layout.addWidget(info_label)
        
        layout.addStretch(1)
        
        # 添加按钮
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)
        
        confirm_button = QPushButton("退出")
        confirm_button.setFixedHeight(42)
        confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: #ffffff;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Segoe UI', '微软雅黑';
                min-width: 100px;
            }
            
            QPushButton:hover {
                background-color: #f5554a;
            }
            
            QPushButton:pressed {
                background-color: #e53935;
            }
        """)
        
        button_layout.addStretch(1)
        button_layout.addWidget(confirm_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号和槽
        confirm_button.clicked.connect(self.accept)
        
        # 添加动画
        self.animation = QPropertyAnimation(self, b"windowOpacity")
        self.animation.setDuration(300)
        self.animation.setStartValue(0.0)
        self.animation.setEndValue(1.0)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self.animation.start()
        
        # 设置整体样式
        self.setStyleSheet("""
            QDialog {
                background-color: #1e1e1e;
                border-radius: 10px;
            }
        """)

def main_verification_window():
    """显示订单验证窗口并处理验证流程"""
    # 检查并请求管理员权限
    check_and_request_admin()
    
    # 根据系统类型设置路径
    d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
    blacklist1_path = os.path.join(d_drive, "blacklist1.json")
    
    # 创建QApplication实例
    app = None
    if not QApplication.instance():
        app = QApplication(sys.argv)
    
    # 显示验证对话框
    dialog = OrderVerificationDialog()
    
    # 在后台更新blacklist2.json
    update_blacklist2_background()
    
    # 在后台更新domain.json
    update_domain_background()
    
    # 设置应用程序图标，优先使用ico文件
    app_icon_path = get_resource_path(os.path.join("icons", "LOGO.ico"))
    if os.path.exists(app_icon_path):
        app_icon = QIcon(app_icon_path)
        app.setWindowIcon(app_icon)
    else:
        # 后备方案，使用PNG图标
        app_icon_path = get_resource_path(os.path.join("images", "LOGO.png"))
    if os.path.exists(app_icon_path):
        app_icon = QIcon(app_icon_path)
        app.setWindowIcon(app_icon)
    
    verification_result = False  # 默认验证失败
    
    # 如果是首次运行（没有blacklist1.json文件）
    if not os.path.exists(blacklist1_path):
        # 创建空的blacklist2.json
        create_empty_blacklist2()
        
        # 显示订单验证对话框
        dialog = OrderVerificationDialog()
        result = dialog.exec()
        
        if result == QDialog.DialogCode.Accepted:
            verification_result = True
    else:
        # 已有订单号，验证是否有效
        if verify_order_number():
            # 显示验证成功对话框
            success_dialog = VerificationSuccessDialog()
            success_dialog.exec()
            verification_result = True
        else:
            # 显示验证失败对话框
            failed_dialog = VerificationFailedDialog()
            failed_dialog.exec()
            verification_result = False
    
    # 如果是独立运行的，关闭Qt应用程序
    if not QApplication.instance():
        app.quit()
    
    # 返回验证结果
    return verification_result

if __name__ == "__main__":
    main_verification_window() 