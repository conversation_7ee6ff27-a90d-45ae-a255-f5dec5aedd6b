import poplib
import email
import random
import string
import time
import re
import logging
from email.header import decode_header
from datetime import datetime, timedelta, timezone
import traceback
import json
import os
import platform

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# POP3服务器配置
POP3_SERVER = 'pop.2925.com'
POP3_PORT = 995
POP3_SSL = True

# 邮箱账号配置
EMAIL_ACCOUNTS = [
    {'email': '<EMAIL>', 'password': 'mnysb123.'},
    {'email': '<EMAIL>', 'password': 'mnysb123.'},
    {'email': '<EMAIL>', 'password': 'mnysb123.'},
    {'email': '<EMAIL>', 'password': 'mnysb123.'},
    {'email': '<EMAIL>', 'password': 'mnysb666.'}
]

# 成功邮箱记录文件路径
def get_successful_emails_path():
    """根据操作系统获取成功邮箱记录文件路径"""
    if platform.system() == "Darwin":  # macOS
        return os.path.join(os.path.expanduser("~"), "successful_emails.txt")
    else:  # Windows 或其他
        return "C:\\successful_emails.txt"

def record_successful_email(email_address, verification_code):
    """记录成功获取验证码的邮箱"""
    try:
        file_path = get_successful_emails_path()
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        with open(file_path, 'a', encoding='utf-8') as f:
            f.write(f"{timestamp} | 邮箱: {email_address} | 验证码: {verification_code}\n")
            
        logger.info(f"成功记录邮箱 {email_address} 到 {file_path}")
        return True
    except Exception as e:
        logger.error(f"记录邮箱失败: {e}")
        return False

class EmailManager:
    def __init__(self):
        self.last_email_time = {}  # 记录每个账号最后一次检查的时间
        for account in EMAIL_ACCOUNTS:
            self.last_email_time[account['email']] = datetime.now(timezone.utc) - timedelta(minutes=30)

        # 加载domain.json文件
        self.domain_map = self.load_domain_map()

    def load_domain_map(self):
        """从domain.json加载邮箱与域名的映射关系"""
        # 根据系统类型设置路径
        domain_path = os.path.join(os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\", "domain.json")
        
        # 尝试加载domain.json文件
        try:
            if os.path.exists(domain_path):
                with open(domain_path, 'r', encoding='utf-8') as f:
                    domain_map = json.load(f)
                logger.info(f"成功从{domain_path}加载域名映射")
                return domain_map
            else:
                logger.warning(f"未找到domain.json文件: {domain_path}，将使用默认配置")
                return None
        except Exception as e:
            logger.error(f"加载domain.json出错: {e}")
            return None

    def generate_sub_email(self):
        """生成子邮箱地址（在@前直接添加随机字符）"""
        if self.domain_map:
            # 从domain.json中随机选择一个邮箱
            email_address = random.choice(list(self.domain_map.keys()))
            
            # 获取该邮箱对应的域名列表
            domains = self.domain_map.get(email_address, [])
            
            if domains:
                # 随机选择一个域名
                domain = random.choice(domains)
                
                # 查找对应的账号和密码
                account = None
                for acc in EMAIL_ACCOUNTS:
                    if acc['email'] == email_address:
                        account = acc
                        break
                
                if not account:
                    logger.warning(f"在EMAIL_ACCOUNTS中未找到匹配的邮箱账号: {email_address}")
                    # 使用旧的方法作为备选
                    return self._generate_sub_email_old()
                
                # 生成6个随机字符（字母和数字）
                random_chars = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
                
                # 创建子邮箱（域名@前随机添加字符）
                sub_email = f"{random_chars}@{domain}"
                
                # 记录使用的账号和域名信息
                logger.info(f"随机选择邮箱账号: {email_address}, 域名: {domain}")
                
                return sub_email
            else:
                logger.warning(f"选择的邮箱 {email_address} 没有对应的域名")
                return self._generate_sub_email_old()
        else:
            # 如果没有找到domain.json或加载失败，使用旧方法
            return self._generate_sub_email_old()
    
    def _generate_sub_email_old(self):
        """旧的子邮箱生成方法（备选方案）"""
        # 随机选择一个邮箱账号
        account = random.choice(EMAIL_ACCOUNTS)
        email_parts = account['email'].split('@')
        
        # 生成6个随机字符（字母和数字）
        random_chars = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
        
        # 创建子邮箱（不使用+号，直接将随机字符添加到用户名后面）
        sub_email = f"{email_parts[0]}{random_chars}@{email_parts[1]}"
        
        # 记录使用的账号信息
        logger.info(f"使用旧方法选择邮箱账号: {account['email']}")
        
        return sub_email

    def get_verification_code(self, email_address, timeout=120, check_interval=3, 
                             keyword=None, sender=None, after_time=None):
        """
        获取指定邮箱的验证码
        
        参数:
            email_address: 要检查的邮箱地址
            timeout: 超时时间（秒）
            check_interval: 检查间隔（秒）
            keyword: 邮件主题或内容中的关键词
            sender: 发件人邮箱地址
            after_time: 只检查此时间之后的邮件
        
        返回:
            验证码字符串或None
        """
        if after_time is None:
            # 默认检查最近30分钟内的邮件
            after_time = datetime.now(timezone.utc) - timedelta(minutes=30)
        elif after_time.tzinfo is None:
            # 确保 after_time 有时区信息
            after_time = after_time.replace(tzinfo=timezone.utc)
            
        # 从email_address中提取域名
        try:
            domain = email_address.split('@')[1]
            logger.info(f"提取到域名: {domain}")
        except IndexError:
            logger.error(f"无法从邮箱地址提取域名: {email_address}")
            return None
        
        # 查找可以接收此域名邮件的邮箱账号
        potential_accounts = []
        if self.domain_map:
            for email_addr, domains in self.domain_map.items():
                if domain in domains:
                    # 找到对应的账号信息
                    for acc in EMAIL_ACCOUNTS:
                        if acc['email'] == email_addr:
                            potential_accounts.append(acc)
                            logger.info(f"找到可接收 {domain} 域名邮件的账号: {email_addr}")
        
        # 如果没有找到匹配的账号，尝试使用旧方法
        if not potential_accounts:
            logger.warning(f"未找到可接收 {domain} 域名邮件的账号，尝试使用旧方法")
            # 旧的方法：根据邮箱后缀匹配
            for acc in EMAIL_ACCOUNTS:
                acc_domain = acc['email'].split('@')[1]
                if email_address.endswith('@' + acc_domain):
                    email_prefix = email_address.split('@')[0]
                    acc_prefix = acc['email'].split('@')[0]
                    if email_prefix.startswith(acc_prefix):
                        potential_accounts.append(acc)
                        break
        
        # 如果仍然没有找到匹配的账号，返回失败
        if not potential_accounts:
            logger.error(f"无法找到匹配的邮箱账号: {email_address}")
            return None
        
        # 从潜在账号中选择一个（如果有多个）
        account = potential_accounts[0]
        logger.info(f"使用账号 {account['email']} 检查验证码")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 连接到POP3服务器
                if POP3_SSL:
                    mail_server = poplib.POP3_SSL(POP3_SERVER, POP3_PORT)
                else:
                    mail_server = poplib.POP3(POP3_SERVER, POP3_PORT)
                
                # 登录
                mail_server.user(account['email'])
                mail_server.pass_(account['password'])
                
                # 获取邮件数量
                email_count = len(mail_server.list()[1])
                logger.info(f"共有 {email_count} 封邮件")
                
                # 检查最新的30封邮件
                start_index = max(1, email_count - 29)  # 确保至少从第1封开始
                logger.info(f"将检查最新的 {min(30, email_count)} 封邮件 (#{start_index} 到 #{email_count})")
                
                # 存储匹配的邮件和验证码
                matched_emails = []
                
                # 从最新的邮件开始检查，最多检查10封
                for i in range(email_count, start_index - 1, -1):
                    # 获取邮件
                    response, lines, octets = mail_server.retr(i)
                    
                    # 解析邮件内容
                    msg_content = b'\r\n'.join(lines).decode('utf-8', errors='ignore')
                    msg = email.message_from_string(msg_content)
                    
                    # 获取收件人
                    to_field = msg.get('To', '')
                    # 严格检查收件人必须与生成的子邮箱完全一致
                    if not to_field or email_address.lower() != to_field.lower().strip():
                        logger.info(f"邮件 #{i} 收件人不匹配，跳过: {to_field} != {email_address}")
                        continue
                    
                    logger.info(f"找到严格匹配收件人的邮件 #{i}: {to_field}")
                    
                    # 获取邮件日期
                    date_str = msg.get('Date')
                    msg_date = datetime.now(timezone.utc)  # 默认使用当前时间，带时区信息
                    
                    if date_str:
                        # 解析日期字符串为datetime对象
                        try:
                            # 处理不同格式的日期
                            date_formats = [
                                '%a, %d %b %Y %H:%M:%S %z',
                                '%a, %d %b %Y %H:%M:%S %Z',
                                '%d %b %Y %H:%M:%S %z',
                                '%a, %d %b %Y %H:%M:%S'
                            ]
                            
                            for fmt in date_formats:
                                try:
                                    parsed_date = datetime.strptime(date_str.strip(), fmt)
                                    # 确保解析后的日期有时区信息
                                    if parsed_date.tzinfo is None:
                                        parsed_date = parsed_date.replace(tzinfo=timezone.utc)
                                    msg_date = parsed_date
                                    break
                                except ValueError:
                                    continue
                            
                            if msg_date is None:
                                # 如果所有格式都失败，尝试一个更宽松的解析
                                date_match = re.search(r'\d{1,2}\s+\w+\s+\d{4}\s+\d{1,2}:\d{1,2}:\d{1,2}', date_str)
                                if date_match:
                                    date_part = date_match.group(0)
                                    try:
                                        parsed_date = datetime.strptime(date_part, '%d %b %Y %H:%M:%S')
                                        # 确保解析后的日期有时区信息
                                        if parsed_date.tzinfo is None:
                                            parsed_date = parsed_date.replace(tzinfo=timezone.utc)
                                        msg_date = parsed_date
                                    except ValueError:
                                        msg_date = datetime.now(timezone.utc)  # 实在解析不了就用当前时间
                        except Exception as e:
                            logger.warning(f"解析邮件日期出错: {e}")
                    
                    # 如果邮件日期早于指定时间，跳过
                    if msg_date < after_time:
                        logger.info(f"邮件 #{i} 日期过早，跳过: {msg_date}")
                        continue
                    
                    # 记录发件人信息，但不做限制
                    from_field = msg.get('From', '')
                    logger.info(f"邮件 #{i} 发件人: {from_field}")
                    
                    # 获取邮件主题
                    subject = msg.get('Subject', '')
                    if subject:
                        subject, encoding = decode_header(subject)[0]
                        if isinstance(subject, bytes):
                            subject = subject.decode(encoding or 'utf-8', errors='ignore')
                    
                    # 获取邮件正文
                    body = ""
                    if msg.is_multipart():
                        for part in msg.walk():
                            content_type = part.get_content_type()
                            if content_type == "text/plain" or content_type == "text/html":
                                try:
                                    payload = part.get_payload(decode=True)
                                    charset = part.get_content_charset() or 'utf-8'
                                    body += payload.decode(charset, errors='ignore')
                                except Exception as e:
                                    logger.warning(f"解析邮件内容出错: {e}")
                    else:
                        try:
                            payload = msg.get_payload(decode=True)
                            charset = msg.get_content_charset() or 'utf-8'
                            body = payload.decode(charset, errors='ignore')
                        except Exception as e:
                            logger.warning(f"解析邮件内容出错: {e}")
                    
                    # 检查关键词（如果指定）
                    if keyword and keyword.lower() not in subject.lower() and keyword.lower() not in body.lower():
                        logger.info(f"邮件 #{i} 不包含关键词 '{keyword}'，跳过")
                        continue
                    
                    # 尝试提取6位数字验证码（排除CSS颜色代码）
                    patterns = [
                        r'验证码[是为]?[:：\s]*?(\d{6})',
                        r'verification code[:\s]*?(\d{6})',
                        r'code[:\s]*?(\d{6})',
                        r'[验證]证码[是为]?[：:]*?(\d{6})',
                        r'[Cc]ode:?\s*(\d{6})',
                        r'您的验证码是[：:\s]*?(\d{6})',
                        r'[Yy]our verification code is[：:\s]*?(\d{6})',
                        r'[Yy]our code is[：:\s]*?(\d{6})',
                        # Augment 特定模式
                        r'[Yy]our Augment verification code is[：:\s]*?(\d{6})',
                        r'Augment[^<>]*?code[^<>]*?(\d{6})',
                        r'Welcome to Augment[^<>]*?(\d{6})',
                        r'verification code for Augment[：:\s]*?(\d{6})'
                    ]
                    
                    # 先尝试使用特定上下文的模式
                    found_code = False
                    for pattern in patterns:
                        match = re.search(pattern, body)
                        if match:
                            verification_code = match.group(1)
                            logger.info(f"邮件 #{i} 找到6位验证码: {verification_code}")
                            
                            # 将匹配的邮件添加到列表中，包含日期和验证码
                            matched_emails.append({
                                'date': msg_date,
                                'code': verification_code,
                                'index': i
                            })
                            found_code = True
                            break
                    
                    # 如果特定模式没有找到，尝试更通用但更安全的方法
                    if not found_code:
                        # 查找所有6位连续数字
                        all_codes = re.findall(r'(?<![#a-fA-F0-9])(\d{6})(?![a-fA-F0-9])', body)
                        
                        if all_codes:
                            # 过滤掉可能是CSS颜色代码的数字（通常以#开头）
                            filtered_codes = [code for code in all_codes if not re.search(r'#[a-fA-F0-9]{0,2}' + code, body)]
                            
                            if filtered_codes:
                                # 使用第一个匹配的验证码
                                verification_code = filtered_codes[0]
                                logger.info(f"邮件 #{i} 找到6位验证码: {verification_code}")
                                
                                # 将匹配的邮件添加到列表中
                                matched_emails.append({
                                    'date': msg_date,
                                    'code': verification_code,
                                    'index': i
                                })
                
                # 关闭连接
                mail_server.quit()
                
                # 如果找到匹配的邮件，返回最新的一封中的验证码
                if matched_emails:
                    # 按邮件索引号排序，索引号越大表示越新
                    matched_emails.sort(key=lambda x: x['index'], reverse=True)
                    newest_email = matched_emails[0]
                    logger.info(f"找到 {len(matched_emails)} 封匹配邮件，返回最新邮件 #{newest_email['index']} 中的验证码: {newest_email['code']}")
                    logger.info(f"邮件日期: {newest_email['date']}")
                    
                    # 记录成功获取验证码的邮箱
                    record_successful_email(email_address, newest_email['code'])
                    
                    return newest_email['code']
                else:
                    logger.info(f"未找到任何匹配的邮件，将在 {check_interval} 秒后重试")
                
            except Exception as e:
                logger.error(f"检查邮件时出错: {e}")
                logger.error(traceback.format_exc())
            
            # 等待一段时间后再次检查
            logger.info(f"本次检查未找到验证码，{check_interval}秒后再次检查...")
            time.sleep(check_interval)
        
        logger.warning(f"超时: 未能在 {timeout} 秒内找到验证码")
        return None

def main():
    """主函数，演示如何使用EmailManager"""
    email_manager = EmailManager()
    
    # 生成一个子邮箱
    email_address = email_manager.generate_sub_email()
    print(f"生成的子邮箱地址: {email_address}")
    
    # 等待用户操作
    print("请使用此邮箱地址注册或登录，然后等待验证码...")
    input("按回车键开始检查验证码...")
    
    # 获取验证码
    verification_code = email_manager.get_verification_code(
        email_address=email_address,
        timeout=180,  # 3分钟超时
        check_interval=3,  # 每3秒检查一次
        keyword=None  # 不限制关键词，只通过收件人和验证码格式筛选
    )
    
    if verification_code:
        print(f"验证码: {verification_code}")
    else:
        print("未能获取验证码，请重试")

if __name__ == "__main__":
    main() 