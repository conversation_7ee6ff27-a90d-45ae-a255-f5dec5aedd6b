# 🎯 2925邮箱生成器 - 集成原软件邮箱服务

## 📧 功能升级说明

您的 `2925_email_generator.py` 已成功集成原软件的2925邮箱服务！现在您可以在一个界面中使用多种邮箱服务。

## 🌟 新增功能

### 1. **.icu** - 新增 ⭐⭐⭐⭐⭐
- **特点**: 完全集成原Cursor工具的2925邮箱服务
- **优势**: 最稳定可靠，与原软件完全兼容
- **功能**: 
  - 支持domain.json域名映射
  - 智能验证码识别
  - POP3邮件接收
  - 自动子邮箱生成

### 2. **Mail.tm** - 保留 ⭐⭐⭐⭐
- **特点**: 功能完整的临时邮箱服务
- **优势**: 支持邮件删除，API稳定

### 3. **Mail.tm (备用)** - 保留 ⭐⭐⭐⭐
- **特点**: Mail.tm的备用实现
- **优势**: 提供冗余保障

## 🚀 使用方法

### 启动程序
```bash
python 2925_email_generator.py
```

### 界面说明
1. **服务选择**: 现在默认使用 ".icu" 服务
2. **邮箱生成**: 点击"生成"按钮生成邮箱
3. **验证码监控**: 点击"开始监控"自动获取验证码
4. **服务切换**: 可以在下拉菜单中切换不同的邮箱服务

### 2925邮箱服务特色功能

#### 1. **Domain.json支持**
- 自动加载 `C:\domain.json` (Windows) 或 `~/domain.json` (macOS)
- 支持多域名映射
- 智能选择最佳域名

#### 2. **增强验证码识别**
支持以下验证码格式：
- **中文模式**: "验证码: 123456"
- **英文模式**: "verification code: 123456"
- **Cursor专用**: "Your Cursor verification code is: 123456"
- **Augment专用**: "Your Augment verification code is: 123456"
- **通用数字**: 自动识别4-8位数字验证码

#### 3. **POP3邮件接收**
- 连接到 `pop.2925.com:995`
- 支持SSL加密连接
- 自动筛选目标邮件

## 🔧 配置文件

### domain.json 示例
```json
{
  "<EMAIL>": [
    "flzt2045.icu",
    "example.com"
  ],
  "<EMAIL>": [
    "test.org",
    "demo.net"
  ]
}
```

### 邮箱账号配置
程序内置以下2925邮箱账号：
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

## 📋 完整使用流程

### 1. Cursor注册/登录流程
1. **启动邮箱生成器** → 选择".icu"
2. **生成邮箱** → 复制生成的邮箱地址
3. **在Cursor中输入邮箱** → 触发验证码发送
4. **启动验证码监控** → 程序自动获取验证码
5. **复制验证码** → 在Cursor中输入完成验证

### 2. 快速操作
- **一键生成**: 直接点击"生成"使用2925服务
- **自动监控**: 生成邮箱后立即开始监控
- **智能识别**: 自动识别多种验证码格式
- **剪贴板复制**: 验证码自动复制到剪贴板

## ⚡ 技术特性

### 多服务架构
- **统一接口**: 所有邮箱服务使用相同的操作界面
- **智能切换**: 服务不可用时可手动切换
- **优先级排序**: 2925服务优先，其他服务备用

### 增强验证码处理
- **多格式支持**: 支持中英文验证码邮件
- **智能过滤**: 排除CSS颜色代码等干扰
- **上下文识别**: 根据邮件内容智能提取
- **实时监控**: 5秒间隔自动检查新邮件

### 用户体验优化
- **科技感界面**: 深色主题，现代化设计
- **实时反馈**: 操作状态实时显示
- **多语言支持**: 中英文界面切换
- **历史记录**: 自动保存使用历史

## 🔄 版本对比

### 升级前 (原版)
- ✅ Mail.tm 服务
- ✅ Mail.tm 备用服务
- ✅ 基础验证码识别

### 升级后 (集成版)
- ✅ **2925.com 原软件服务** (新增)
- ✅ Mail.tm 服务
- ✅ Mail.tm 备用服务
- ✅ **增强验证码识别** (升级)
- ✅ **Domain.json 支持** (新增)
- ✅ **POP3 邮件接收** (新增)
- ✅ **Cursor/Augment 专用模式** (新增)

## ⚠️ 注意事项

### 使用建议
1. **优先使用2925服务**: 最稳定，与原软件完全兼容
2. **确保配置文件**: 检查domain.json文件是否存在
3. **网络连接**: 确保能访问pop.2925.com
4. **及时获取验证码**: 验证码有时效性限制

### 常见问题解决
1. **2925服务连接失败**: 检查网络连接和防火墙设置
2. **验证码获取失败**: 尝试切换到备用邮箱服务
3. **Domain.json未找到**: 程序会自动使用默认配置
4. **邮箱生成失败**: 切换到Mail.tm服务作为备用

## 🎯 技术架构

### 新增组件
```
2925_email_generator.py
├── Original2925Provider          # 2925邮箱服务提供者
│   ├── load_domain_map()         # 加载域名映射
│   ├── generate_email()          # 生成子邮箱
│   ├── get_messages()            # POP3邮件接收
│   └── extract_verification()    # 验证码提取
├── Enhanced Patterns             # 增强验证码模式
└── Integrated UI                 # 统一用户界面
```

### 服务优先级
1. **.icu** - 默认首选
2. **Mail.tm** - 备用选择
3. **Mail.tm (备用)** - 最后备选

## 🚀 开发者信息

- **原项目**: 2925邮箱生成器
- **集成版本**: v2.0 Enhanced
- **新增服务**: 2925.com 原软件邮箱服务
- **兼容性**: 完全向下兼容
- **技术栈**: Python + CustomTkinter + POP3

## 📞 使用支持

### 故障排除步骤
1. **检查网络连接**
2. **验证配置文件**
3. **尝试切换邮箱服务**
4. **查看控制台输出**
5. **重启程序**

### 成功标志
- ✅ 程序启动显示".icu"选项
- ✅ 能够成功生成子邮箱地址
- ✅ 验证码监控正常工作
- ✅ 验证码自动复制到剪贴板

---

**🎉 现在您可以在熟悉的界面中享受原软件2925邮箱服务的强大功能！**
