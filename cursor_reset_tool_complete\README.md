# Cursor Pro 无限续杯工具 v2.2.3 - 完整版

这是从原始exe文件完全逆向工程重建的Cursor编辑器"无限续杯"工具，包含所有原始功能。

## 🎯 完整功能列表

### 1. 📋 订单验证系统
- **19位订单号验证** - 严格的订单格式检查
- **黑名单系统** - 防止重复使用订单号
- **自动更新** - 从Gitee仓库自动更新黑名单
- **PyQt6 GUI** - 美观的图形界面
- **多重验证** - API、原始URL、Git克隆等多种下载方式

### 2. 📧 邮箱管理系统
- **临时邮箱生成** - 自动生成随机子邮箱
- **POP3邮件接收** - 实时监控邮件
- **验证码提取** - 智能识别6位验证码
- **多账号支持** - 支持多个邮箱账号
- **域名映射** - 灵活的域名配置系统
- **成功记录** - 自动记录成功的验证码

### 3. 🔄 机器ID重置
- **完整重置** - 重置所有Cursor标识符
- **SQLite更新** - 更新本地数据库
- **注册表修改** - Windows系统级ID更新
- **自动备份** - 修改前自动备份
- **跨平台支持** - Windows、macOS、Linux

### 4. ⚙️ 进程管理
- **优雅关闭** - 安全关闭Cursor进程
- **进程监控** - 实时监控运行状态
- **超时处理** - 智能超时机制

### 5. 🧹 状态清理
- **数据库清理** - 删除状态数据库
- **用户数据清理** - 清理相关配置
- **安全删除** - 确保文件完全删除

### 6. 🎨 图形界面
- **PyQt6界面** - 现代化GUI设计
- **动画效果** - 流畅的界面动画
- **主题支持** - 深色主题界面
- **多语言支持** - 中英文界面

## 🚀 使用方法

### 快速开始
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行程序
python main.py
```

### 功能说明

#### 订单验证
程序启动时会自动进行订单验证：
- 首次使用需要输入19位订单号
- 系统会检查订单号是否已被使用
- 验证通过后可以使用所有功能

#### 邮箱管理
```python
from email_manager import EmailManager

# 创建邮箱管理器
manager = EmailManager()

# 生成临时邮箱
email = manager.generate_sub_email()

# 获取验证码
code = manager.get_verification_code(email)
```

#### 机器ID重置
- 选择菜单选项1
- 系统会自动备份现有配置
- 生成新的机器标识符
- 更新所有相关文件

## 📁 项目结构

```
cursor_reset_tool_complete/
├── main.py                    # 主程序入口
├── verify_order.py            # 订单验证系统
├── email_manager.py           # 邮箱管理系统
├── reset_machine_manual.py    # 机器ID重置
├── quit_cursor.py             # 进程管理
├── delete_state_db.py         # 状态清理
├── verify_config.py           # 验证配置
├── config.py                  # 配置管理
├── utils.py                   # 工具函数
├── logo.py                    # Logo显示
├── requirements.txt           # 依赖列表
├── README.md                  # 说明文档
└── image/
    └── logo.png              # Logo图片
```

## 🔧 技术细节

### 邮箱系统配置
- **POP3服务器**: pop.2925.com:995 (SSL)
- **支持域名**: 2925.com及其子域名
- **验证码模式**: 6位数字验证码
- **超时设置**: 120秒默认超时

### 重置的标识符
- `telemetry.devDeviceId` - 设备ID (UUID)
- `telemetry.machineId` - 机器ID (SHA256)
- `telemetry.macMachineId` - Mac机器ID (SHA512)
- `telemetry.sqmId` - SQM ID (GUID)
- `storage.serviceMachineId` - 服务机器ID

### 修改的文件
- `storage.json` - Cursor配置文件
- `state.vscdb` - SQLite数据库
- `machineId` - 机器ID文件
- Windows注册表 - MachineGuid, SQMClient

### 黑名单系统
- `blacklist1.json` - 本地订单记录
- `blacklist2.json` - 远程黑名单（从Gitee同步）
- 自动更新机制，防止重复使用

## ⚠️ 注意事项

### 系统要求
- Python 3.7+
- Windows 10/11, macOS 10.14+, 或 Linux
- 管理员权限（Windows）
- 网络连接（用于验证和邮箱功能）

### 使用前准备
1. **关闭Cursor** - 使用前请完全关闭Cursor编辑器
2. **管理员权限** - Windows系统需要管理员权限
3. **网络连接** - 确保网络连接正常
4. **备份数据** - 建议备份重要的Cursor配置

### 安全提醒
- 本工具会修改系统文件和注册表
- 使用前会自动创建备份
- 请在测试环境中先行验证
- 不建议在生产环境中使用

## 🛠️ 故障排除

### 常见问题

1. **订单验证失败**
   - 检查订单号格式（19位数字）
   - 确认网络连接正常
   - 检查是否已被使用

2. **邮箱功能异常**
   - 检查网络连接
   - 确认POP3服务器可访问
   - 检查邮箱账号配置

3. **权限不足**
   - Windows：以管理员身份运行
   - macOS/Linux：使用sudo运行

4. **Cursor路径错误**
   - 检查Cursor是否正确安装
   - 手动设置环境变量

## 📜 免责声明

本工具仅供学习和研究目的使用。使用者应当：
- 遵守相关法律法规
- 遵守软件许可协议
- 承担使用风险
- 不用于商业用途

作者不对使用本工具造成的任何后果承担责任。

## 🤝 贡献者

- **原作者**: Pin Studios (yeongpin)
- **逆向工程**: AI Assistant
- **其他贡献者**: BasaiCorp, aliensb, handwerk2016, 等

## 📄 许可证

本项目基于原始软件进行逆向工程重建，仅供学习研究使用。

---

**GitHub**: https://github.com/yeongpin/cursor-free-vip
**版本**: v2.2.3 (完整重建版)
