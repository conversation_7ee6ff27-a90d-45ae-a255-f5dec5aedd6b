# main_console.py - 纯控制台版本（无GUI）
import os
import sys
import platform
from colorama import Fore, Style, init
import locale
from config import get_config, force_update_config
from utils import get_user_documents_path

# Initialize colorama
init()

# Define emoji and color constants
EMOJI = {
    "FILE": "📄",
    "BACKUP": "💾",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "INFO": "ℹ️",
    "RESET": "🔄",
    "MENU": "📋",
    "ARROW": "➜",
    "LANG": "🌐",
    "ADMIN": "🔐",
    "EMAIL": "📧",
    "PROCESS": "⚙️",
    "CLEAN": "🧹",
    "ORDER": "💰"
}

class Translator:
    def __init__(self):
        self.translations = {}
        self.current_language = 'zh_cn'  # 默认中文
        self.fallback_language = 'en'

    def get(self, key, **kwargs):
        """Get translated text with fallback support"""
        try:
            return key.format(**kwargs) if kwargs else key
        except Exception:
            return key

# Create translator instance
translator = Translator()

def print_menu():
    """Print menu options"""
    print(f"\n{Fore.CYAN}{EMOJI['MENU']} 菜单:{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}{'─' * 70}{Style.RESET_ALL}")
    
    print(f"{Fore.GREEN}1{Style.RESET_ALL}. {EMOJI['RESET']} 重置 Cursor 机器 ID")
    print(f"{Fore.GREEN}2{Style.RESET_ALL}. {EMOJI['EMAIL']} 邮箱管理工具（演示）")
    print(f"{Fore.GREEN}3{Style.RESET_ALL}. {EMOJI['PROCESS']} 关闭 Cursor 进程")
    print(f"{Fore.GREEN}4{Style.RESET_ALL}. {EMOJI['CLEAN']} 清理状态数据库")
    print(f"{Fore.GREEN}5{Style.RESET_ALL}. {EMOJI['ORDER']} 订单验证（跳过GUI）")
    print(f"{Fore.GREEN}0{Style.RESET_ALL}. {EMOJI['ERROR']} 退出")
    
    print(f"{Fore.YELLOW}{'─' * 70}{Style.RESET_ALL}")

def run_machine_reset():
    """运行机器ID重置"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在启动机器ID重置...{Style.RESET_ALL}")
        import reset_machine_manual
        reset_machine_manual.run(translator)
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 机器ID重置错误: {e}{Style.RESET_ALL}")

def run_email_manager_demo():
    """运行邮箱管理器演示"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在演示邮箱管理器功能...{Style.RESET_ALL}")
        import email_manager
        
        # 创建邮箱管理器实例
        manager = email_manager.EmailManager()
        
        # 生成演示邮箱
        demo_email = manager.generate_sub_email()
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 生成的临时邮箱: {demo_email}{Style.RESET_ALL}")
        
        # 显示配置信息
        print(f"{Fore.CYAN}{EMOJI['INFO']} POP3服务器: {email_manager.POP3_SERVER}:{email_manager.POP3_PORT}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{EMOJI['INFO']} 支持的邮箱账号数量: {len(email_manager.EMAIL_ACCOUNTS)}{Style.RESET_ALL}")
        
        print(f"{Fore.YELLOW}{EMOJI['INFO']} 邮箱管理器演示完成{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 邮箱管理器错误: {e}{Style.RESET_ALL}")

def run_quit_cursor():
    """运行Cursor进程关闭"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在关闭Cursor进程...{Style.RESET_ALL}")
        import quit_cursor
        quit_cursor.quit_cursor(translator)
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 进程关闭错误: {e}{Style.RESET_ALL}")

def run_delete_state():
    """运行状态数据库清理"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在清理状态数据库...{Style.RESET_ALL}")
        import delete_state_db
        delete_state_db.delete_state_db()
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 状态清理错误: {e}{Style.RESET_ALL}")

def run_order_verification_console():
    """运行订单验证（控制台版本）"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 订单验证功能（控制台模式）{Style.RESET_ALL}")
        
        # 导入验证配置
        import verify_config
        urls = verify_config.get_gitee_urls()
        
        print(f"{Fore.CYAN}{EMOJI['INFO']} Gitee仓库配置:{Style.RESET_ALL}")
        print(f"  用户名: {verify_config.GITEE_USERNAME}")
        print(f"  仓库名: {verify_config.REPO_NAME}")
        print(f"  API URL: {urls['api_url'][:50]}...")
        
        # 检查本地文件
        import platform
        d_drive = os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\"
        blacklist1_path = os.path.join(d_drive, "blacklist1.json")
        blacklist2_path = os.path.join(d_drive, "blacklist2.json")
        
        print(f"{Fore.CYAN}{EMOJI['INFO']} 检查本地文件:{Style.RESET_ALL}")
        print(f"  blacklist1.json: {'存在' if os.path.exists(blacklist1_path) else '不存在'}")
        print(f"  blacklist2.json: {'存在' if os.path.exists(blacklist2_path) else '不存在'}")
        
        print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 订单验证模块检查完成{Style.RESET_ALL}")
        
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 订单验证错误: {e}{Style.RESET_ALL}")

def main():
    # 导入 logo 模块
    try:
        from logo import print_logo
        print_logo()
    except ImportError:
        print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Cursor 无限续杯工具 - 控制台版{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
    
    print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 程序启动成功！{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{EMOJI['INFO']} 这是控制台版本，跳过GUI组件{Style.RESET_ALL}")
    
    # Initialize configuration
    try:
        config = get_config(translator)
        if config:
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 配置系统初始化成功{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 配置系统初始化警告{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.YELLOW}{EMOJI['INFO']} 配置初始化警告: {e}{Style.RESET_ALL}")

    print_menu()
    
    while True:
        try:
            choice = input(f"\n{EMOJI['ARROW']} {Fore.CYAN}请输入选择 (0-5): {Style.RESET_ALL}")

            if choice == "0":
                print(f"\n{Fore.YELLOW}{EMOJI['INFO']} 退出中...{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
                return
            elif choice == "1":
                run_machine_reset()
                print_menu()
            elif choice == "2":
                run_email_manager_demo()
                print_menu()
            elif choice == "3":
                run_quit_cursor()
                print_menu()
            elif choice == "4":
                run_delete_state()
                print_menu()
            elif choice == "5":
                run_order_verification_console()
                print_menu()
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} 无效的选择，请输入0-5{Style.RESET_ALL}")
                print_menu()

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}{EMOJI['INFO']} 程序已终止{Style.RESET_ALL}")
            print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
            return
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 发生错误: {str(e)}{Style.RESET_ALL}")
            print_menu()

if __name__ == "__main__":
    main()
