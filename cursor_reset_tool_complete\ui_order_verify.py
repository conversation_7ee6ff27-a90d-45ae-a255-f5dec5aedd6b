#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单验证GUI窗口
"""

import sys
import os
import json
import platform
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QTextEdit, QProgressBar,
                            QMessageBox, QGroupBox, QFrame, QApplication)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon

class OrderVerifyThread(QThread):
    """订单验证线程"""
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    
    def __init__(self, order_number):
        super().__init__()
        self.order_number = order_number
    
    def run(self):
        """运行验证"""
        try:
            self.message.emit("正在验证订单格式...")
            self.progress.emit(20)
            
            # 验证订单号格式
            if not self.validate_order_format():
                self.finished.emit(False, "订单号格式不正确，必须是19位数字")
                return
            
            self.message.emit("正在检查本地黑名单...")
            self.progress.emit(40)
            
            # 检查本地黑名单
            if self.check_local_blacklist():
                self.finished.emit(False, "此订单号已被使用")
                return
            
            self.message.emit("正在下载远程黑名单...")
            self.progress.emit(60)
            
            # 下载远程黑名单
            if not self.download_remote_blacklist():
                self.message.emit("远程黑名单下载失败，使用本地验证...")
            
            self.message.emit("正在进行最终验证...")
            self.progress.emit(80)
            
            # 最终验证
            if self.final_verification():
                self.save_order_to_blacklist()
                self.progress.emit(100)
                self.finished.emit(True, "订单验证成功！")
            else:
                self.finished.emit(False, "订单验证失败")
                
        except Exception as e:
            self.finished.emit(False, f"验证过程出错: {str(e)}")
    
    def validate_order_format(self):
        """验证订单号格式"""
        return len(self.order_number) == 19 and self.order_number.isdigit()
    
    def check_local_blacklist(self):
        """检查本地黑名单"""
        try:
            # 根据系统选择路径
            if platform.system() == "Darwin":
                blacklist_path = os.path.expanduser("~/blacklist1.json")
            else:
                blacklist_path = "C:/blacklist1.json"
            
            if os.path.exists(blacklist_path):
                with open(blacklist_path, 'r', encoding='utf-8') as f:
                    blacklist = json.load(f)
                    return self.order_number in blacklist
            return False
        except:
            return False
    
    def download_remote_blacklist(self):
        """下载远程黑名单"""
        try:
            import requests
            from verify_config import get_gitee_urls
            
            urls = get_gitee_urls()
            
            # 尝试多个URL
            for url_key in ['api_url', 'original_url', 'git_clone_url']:
                if url_key in urls:
                    try:
                        response = requests.get(urls[url_key], timeout=10)
                        if response.status_code == 200:
                            # 保存远程黑名单
                            if platform.system() == "Darwin":
                                blacklist_path = os.path.expanduser("~/blacklist2.json")
                            else:
                                blacklist_path = "C:/blacklist2.json"
                            
                            with open(blacklist_path, 'w', encoding='utf-8') as f:
                                f.write(response.text)
                            return True
                    except:
                        continue
            return False
        except:
            return False
    
    def final_verification(self):
        """最终验证"""
        try:
            # 检查远程黑名单
            if platform.system() == "Darwin":
                blacklist_path = os.path.expanduser("~/blacklist2.json")
            else:
                blacklist_path = "C:/blacklist2.json"
            
            if os.path.exists(blacklist_path):
                with open(blacklist_path, 'r', encoding='utf-8') as f:
                    blacklist = json.load(f)
                    if self.order_number in blacklist:
                        return False
            
            return True
        except:
            return True  # 如果检查失败，默认通过
    
    def save_order_to_blacklist(self):
        """保存订单到黑名单"""
        try:
            if platform.system() == "Darwin":
                blacklist_path = os.path.expanduser("~/blacklist1.json")
            else:
                blacklist_path = "C:/blacklist1.json"
            
            blacklist = []
            if os.path.exists(blacklist_path):
                with open(blacklist_path, 'r', encoding='utf-8') as f:
                    blacklist = json.load(f)
            
            if self.order_number not in blacklist:
                blacklist.append(self.order_number)
                
                with open(blacklist_path, 'w', encoding='utf-8') as f:
                    json.dump(blacklist, f, indent=2)
        except:
            pass

class OrderVerifyDialog(QDialog):
    """订单验证对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.verify_thread = None
        self.init_ui()
        self.apply_style()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("订单验证 - Cursor Pro")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # 主布局
        layout = QVBoxLayout(self)
        
        # 标题区域
        self.create_header(layout)
        
        # 输入区域
        self.create_input_section(layout)
        
        # 进度区域
        self.create_progress_section(layout)
        
        # 按钮区域
        self.create_button_section(layout)
    
    def create_header(self, parent_layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_layout = QVBoxLayout(header_frame)
        
        title_label = QLabel("订单验证")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #00d4aa; margin: 10px;")
        
        desc_label = QLabel("请输入19位订单号进行验证")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("font-size: 14px; color: #888; margin-bottom: 20px;")
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(desc_label)
        
        parent_layout.addWidget(header_frame)
    
    def create_input_section(self, parent_layout):
        """创建输入区域"""
        input_group = QGroupBox("订单信息")
        input_layout = QVBoxLayout(input_group)
        
        # 订单号输入
        order_label = QLabel("订单号:")
        self.order_input = QLineEdit()
        self.order_input.setPlaceholderText("请输入19位数字订单号")
        self.order_input.setMaxLength(19)
        self.order_input.textChanged.connect(self.on_order_changed)
        
        # 格式提示
        format_label = QLabel("格式: 19位纯数字，例如: 1234567890123456789")
        format_label.setStyleSheet("font-size: 12px; color: #666; font-style: italic;")
        
        input_layout.addWidget(order_label)
        input_layout.addWidget(self.order_input)
        input_layout.addWidget(format_label)
        
        parent_layout.addWidget(input_group)
    
    def create_progress_section(self, parent_layout):
        """创建进度区域"""
        progress_group = QGroupBox("验证进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # 状态标签
        self.status_label = QLabel("等待输入订单号...")
        self.status_label.setStyleSheet("color: #888;")
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(80)
        self.result_text.setReadOnly(True)
        self.result_text.setVisible(False)
        
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.status_label)
        progress_layout.addWidget(self.result_text)
        
        parent_layout.addWidget(progress_group)
    
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 验证按钮
        self.verify_btn = QPushButton("🔍 开始验证")
        self.verify_btn.setMinimumHeight(40)
        self.verify_btn.setEnabled(False)
        self.verify_btn.clicked.connect(self.start_verification)
        
        # 取消按钮
        self.cancel_btn = QPushButton("❌ 取消")
        self.cancel_btn.setMinimumHeight(40)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 确定按钮
        self.ok_btn = QPushButton("✅ 确定")
        self.ok_btn.setMinimumHeight(40)
        self.ok_btn.setVisible(False)
        self.ok_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(self.verify_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.ok_btn)
        
        parent_layout.addLayout(button_layout)
    
    def apply_style(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #34495e;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #00d4aa;
            }
            QLineEdit {
                background-color: #34495e;
                border: 2px solid #4a6741;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
                color: #ecf0f1;
            }
            QLineEdit:focus {
                border-color: #00d4aa;
            }
            QTextEdit {
                background-color: #34495e;
                border: 1px solid #4a6741;
                border-radius: 4px;
                padding: 5px;
                color: #ecf0f1;
            }
            QPushButton {
                background-color: #00d4aa;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #00b894;
            }
            QPushButton:pressed {
                background-color: #00a085;
            }
            QPushButton:disabled {
                background-color: #7f8c8d;
                color: #bdc3c7;
            }
            QProgressBar {
                border: 1px solid #34495e;
                border-radius: 4px;
                text-align: center;
                background-color: #34495e;
            }
            QProgressBar::chunk {
                background-color: #00d4aa;
                border-radius: 3px;
            }
        """)
    
    def on_order_changed(self, text):
        """订单号输入变化"""
        # 只允许数字
        filtered_text = ''.join(filter(str.isdigit, text))
        if filtered_text != text:
            self.order_input.setText(filtered_text)
            return
        
        # 检查长度
        if len(filtered_text) == 19:
            self.verify_btn.setEnabled(True)
            self.status_label.setText("订单号格式正确，可以开始验证")
            self.status_label.setStyleSheet("color: #00d4aa;")
        else:
            self.verify_btn.setEnabled(False)
            self.status_label.setText(f"订单号长度: {len(filtered_text)}/19")
            self.status_label.setStyleSheet("color: #888;")
    
    def start_verification(self):
        """开始验证"""
        order_number = self.order_input.text().strip()
        
        if len(order_number) != 19 or not order_number.isdigit():
            QMessageBox.warning(self, "格式错误", "请输入19位数字订单号")
            return
        
        # 禁用输入和按钮
        self.order_input.setEnabled(False)
        self.verify_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.result_text.setVisible(False)
        
        # 启动验证线程
        self.verify_thread = OrderVerifyThread(order_number)
        self.verify_thread.finished.connect(self.on_verification_finished)
        self.verify_thread.progress.connect(self.progress_bar.setValue)
        self.verify_thread.message.connect(self.status_label.setText)
        self.verify_thread.start()
    
    def on_verification_finished(self, success, message):
        """验证完成"""
        self.progress_bar.setVisible(False)
        self.result_text.setVisible(True)
        
        if success:
            self.result_text.setHtml(f'<div style="color: #00d4aa; font-weight: bold;">✅ {message}</div>')
            self.status_label.setText("验证成功！")
            self.status_label.setStyleSheet("color: #00d4aa;")
            
            # 显示确定按钮
            self.verify_btn.setVisible(False)
            self.cancel_btn.setText("关闭")
            self.ok_btn.setVisible(True)
            
        else:
            self.result_text.setHtml(f'<div style="color: #e74c3c; font-weight: bold;">❌ {message}</div>')
            self.status_label.setText("验证失败")
            self.status_label.setStyleSheet("color: #e74c3c;")
            
            # 重新启用输入
            self.order_input.setEnabled(True)
            self.verify_btn.setEnabled(True)

def show_order_verify_dialog(parent=None):
    """显示订单验证对话框"""
    dialog = OrderVerifyDialog(parent)
    return dialog.exec() == QDialog.DialogCode.Accepted

def main():
    """测试函数"""
    app = QApplication(sys.argv)
    
    dialog = OrderVerifyDialog()
    result = dialog.exec()
    
    print(f"验证结果: {'成功' if result == QDialog.DialogCode.Accepted else '取消'}")

if __name__ == "__main__":
    main()
