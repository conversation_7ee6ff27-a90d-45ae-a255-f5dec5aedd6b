#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试脚本
"""

import os
import sys
from colorama import Fore, Style, init

# 初始化colorama
init()

def test_imports():
    """测试所有模块导入"""
    print(f"{Fore.CYAN}=== 测试模块导入 ==={Style.RESET_ALL}")
    
    modules = [
        'config',
        'utils', 
        'logo',
        'verify_config',
        'email_manager',
        'quit_cursor',
        'delete_state_db',
        'reset_machine_manual'
    ]
    
    success_count = 0
    for module in modules:
        try:
            __import__(module)
            print(f"{Fore.GREEN}✅ {module}{Style.RESET_ALL}")
            success_count += 1
        except Exception as e:
            print(f"{Fore.RED}❌ {module}: {e}{Style.RESET_ALL}")
    
    print(f"\n{Fore.CYAN}导入结果: {success_count}/{len(modules)} 成功{Style.RESET_ALL}")
    return success_count == len(modules)

def test_basic_functions():
    """测试基本功能"""
    print(f"\n{Fore.CYAN}=== 测试基本功能 ==={Style.RESET_ALL}")
    
    try:
        # 测试logo显示
        from logo import print_logo
        print(f"{Fore.GREEN}✅ Logo显示功能正常{Style.RESET_ALL}")
        
        # 测试配置
        from config import get_config
        config = get_config()
        if config:
            print(f"{Fore.GREEN}✅ 配置系统正常{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}❌ 配置系统异常{Style.RESET_ALL}")
            
        # 测试工具函数
        from utils import get_user_documents_path
        docs_path = get_user_documents_path()
        print(f"{Fore.GREEN}✅ 工具函数正常: {docs_path}{Style.RESET_ALL}")
        
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ 基本功能测试失败: {e}{Style.RESET_ALL}")
        return False

def test_email_manager():
    """测试邮箱管理器"""
    print(f"\n{Fore.CYAN}=== 测试邮箱管理器 ==={Style.RESET_ALL}")
    
    try:
        from email_manager import EmailManager
        manager = EmailManager()
        
        # 测试生成邮箱
        email = manager.generate_sub_email()
        print(f"{Fore.GREEN}✅ 邮箱生成功能正常: {email}{Style.RESET_ALL}")
        
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ 邮箱管理器测试失败: {e}{Style.RESET_ALL}")
        return False

def test_verify_config():
    """测试验证配置"""
    print(f"\n{Fore.CYAN}=== 测试验证配置 ==={Style.RESET_ALL}")
    
    try:
        from verify_config import get_gitee_urls, get_domain_urls
        
        gitee_urls = get_gitee_urls()
        domain_urls = get_domain_urls()
        
        print(f"{Fore.GREEN}✅ Gitee URLs: {gitee_urls['api_url'][:50]}...{Style.RESET_ALL}")
        print(f"{Fore.GREEN}✅ Domain URLs: {domain_urls['api_url'][:50]}...{Style.RESET_ALL}")
        
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ 验证配置测试失败: {e}{Style.RESET_ALL}")
        return False

def main():
    """主测试函数"""
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Cursor 无限续杯工具 - 完整版测试{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    tests = [
        ("模块导入", test_imports),
        ("基本功能", test_basic_functions),
        ("邮箱管理器", test_email_manager),
        ("验证配置", test_verify_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"{Fore.RED}❌ {test_name} 测试异常: {e}{Style.RESET_ALL}")
    
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}测试结果: {passed}/{total} 通过{Style.RESET_ALL}")
    
    if passed == total:
        print(f"{Fore.GREEN}🎉 所有测试通过！完整版工具可以正常使用。{Style.RESET_ALL}")
        return True
    else:
        print(f"{Fore.YELLOW}⚠️  部分测试失败，请检查相关功能。{Style.RESET_ALL}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
