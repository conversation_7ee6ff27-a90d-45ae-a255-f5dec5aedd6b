#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Free VIP - 完整版启动器
集成2925邮箱服务的原始风格版本
"""

import sys
import os
import platform
from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont, QPainter, QColor
from colorama import Fore, Style, init

# 初始化colorama
init()

def show_splash_screen(app):
    """显示启动画面"""
    try:
        # 创建启动画面
        splash_pixmap = QPixmap(400, 300)
        splash_pixmap.fill(QColor(26, 26, 26))  # 深色背景
        
        # 绘制启动画面
        painter = QPainter(splash_pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # 设置字体
        title_font = QFont("Microsoft YaHei", 24, QFont.Weight.Bold)
        subtitle_font = QFont("Microsoft YaHei", 14)
        
        # 绘制标题
        painter.setFont(title_font)
        painter.setPen(QColor(139, 92, 246))  # 紫色
        painter.drawText(splash_pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎯 Cursor Free VIP")
        
        # 绘制副标题
        painter.setFont(subtitle_font)
        painter.setPen(QColor(255, 255, 255))
        title_rect = painter.fontMetrics().boundingRect("🎯 Cursor Free VIP")
        subtitle_y = splash_pixmap.height() // 2 + title_rect.height() + 20
        painter.drawText(20, subtitle_y, splash_pixmap.width() - 40, 30, 
                        Qt.AlignmentFlag.AlignCenter, "v2.2.3 - 集成2925邮箱服务")
        
        # 绘制版权信息
        painter.setPen(QColor(176, 176, 176))
        painter.drawText(20, splash_pixmap.height() - 30, splash_pixmap.width() - 40, 20,
                        Qt.AlignmentFlag.AlignCenter, "Pin Studios - 原始风格版本")
        
        painter.end()
        
        # 创建启动画面窗口
        splash = QSplashScreen(splash_pixmap)
        splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
        splash.show()
        
        # 显示消息
        splash.showMessage("正在初始化...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, QColor(255, 255, 255))
        app.processEvents()
        
        return splash
    except Exception as e:
        print(f"启动画面创建失败: {str(e)}")
        return None

def check_admin_privileges():
    """检查管理员权限"""
    if platform.system() == 'Windows':
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False
    return True

def request_admin_privileges():
    """请求管理员权限"""
    if platform.system() != 'Windows':
        return False
        
    try:
        import ctypes
        args = [sys.executable] + sys.argv
        
        # Request elevation via ShellExecute
        ctypes.windll.shell32.ShellExecuteW(None, "runas", args[0], " ".join('"' + arg + '"' for arg in args[1:]), None, 1)
        return True
    except Exception as e:
        print(f"无法以管理员权限重启: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    missing_deps = []
    
    required_packages = [
        ("PyQt6", "PyQt6图形界面库"),
        ("requests", "HTTP请求库"),
        ("colorama", "终端颜色库"),
        ("psutil", "进程管理库")
    ]
    
    for package, description in required_packages:
        try:
            __import__(package.lower() if package != "PyQt6" else "PyQt6")
            print(f"✅ {package} - {description}")
        except ImportError:
            missing_deps.append(package)
            print(f"❌ {package} - {description} (缺失)")
    
    return missing_deps

def main():
    """主函数"""
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}🎯 Cursor Free VIP v2.2.3 - 完整版启动器{Style.RESET_ALL}")
    print(f"{Fore.CYAN}集成2925邮箱服务的原始风格版本{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    
    # 检查依赖
    print(f"\n{Fore.YELLOW}📦 检查依赖包...{Style.RESET_ALL}")
    missing_deps = check_dependencies()
    
    if missing_deps:
        print(f"\n{Fore.RED}❌ 缺少依赖包: {', '.join(missing_deps)}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}请运行以下命令安装:{Style.RESET_ALL}")
        print(f"{Fore.GREEN}pip install {' '.join(missing_deps)}{Style.RESET_ALL}")
        input("\n按回车键退出...")
        return
    
    print(f"{Fore.GREEN}✅ 所有依赖包检查完成{Style.RESET_ALL}")
    
    # 检查管理员权限（仅Windows）
    if platform.system() == 'Windows' and not check_admin_privileges():
        print(f"\n{Fore.YELLOW}⚠️  建议以管理员权限运行以获得最佳体验{Style.RESET_ALL}")
        response = input(f"{Fore.CYAN}是否以管理员权限重启？(y/n): {Style.RESET_ALL}").lower()
        if response in ['y', 'yes', '是']:
            if request_admin_privileges():
                print(f"{Fore.CYAN}正在以管理员权限重启...{Style.RESET_ALL}")
                sys.exit(0)
            else:
                print(f"{Fore.YELLOW}⚠️  无法获取管理员权限，继续以普通权限运行{Style.RESET_ALL}")
    
    # 创建QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Cursor Free VIP")
    app.setApplicationVersion("2.2.3")
    app.setOrganizationName("Pin Studios")
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 显示启动画面
    print(f"\n{Fore.CYAN}🚀 正在启动图形界面...{Style.RESET_ALL}")
    splash = show_splash_screen(app)
    
    try:
        # 更新启动画面消息
        if splash:
            splash.showMessage("正在加载订单验证模块...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, QColor(255, 255, 255))
            app.processEvents()
        
        # 导入订单验证模块
        from ui_order_original import show_original_order_dialog
        
        if splash:
            splash.showMessage("正在进行订单验证...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, QColor(255, 255, 255))
            app.processEvents()
        
        print(f"{Fore.CYAN}📋 正在进行订单验证...{Style.RESET_ALL}")
        
        # 显示订单验证对话框
        order_verified = show_original_order_dialog()
        
        if not order_verified:
            print(f"{Fore.RED}❌ 订单验证失败或被取消{Style.RESET_ALL}")
            if splash:
                splash.close()
            return
        
        print(f"{Fore.GREEN}✅ 订单验证成功{Style.RESET_ALL}")
        
        if splash:
            splash.showMessage("正在加载主界面模块...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, QColor(255, 255, 255))
            app.processEvents()
        
        # 导入主界面模块
        from ui_original_style import OriginalMainWindow
        
        if splash:
            splash.showMessage("正在初始化邮箱服务...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, QColor(255, 255, 255))
            app.processEvents()
        
        # 创建主窗口
        print(f"{Fore.CYAN}🚀 正在启动主界面...{Style.RESET_ALL}")
        main_window = OriginalMainWindow()
        
        # 关闭启动画面
        if splash:
            splash.finish(main_window)
        
        # 居中显示主窗口
        screen = app.primaryScreen().geometry()
        main_window.move(
            (screen.width() - main_window.width()) // 2,
            (screen.height() - main_window.height()) // 2
        )
        
        main_window.show()
        
        print(f"{Fore.GREEN}✅ 原始风格GUI界面启动成功！{Style.RESET_ALL}")
        print(f"{Fore.CYAN}📧 已集成2925邮箱服务和多种备用邮箱服务{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
        print(f"{Fore.GREEN}🎉 享受无限续杯的乐趣！{Style.RESET_ALL}")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except ImportError as e:
        error_msg = f"GUI模块导入失败: {str(e)}\n\n请确保已安装PyQt6:\npip install PyQt6"
        print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
        
        if splash:
            splash.close()
        
        # 尝试显示错误对话框
        try:
            QMessageBox.critical(None, "导入错误", error_msg)
        except:
            pass
        
        # 回退到控制台版本
        print(f"{Fore.YELLOW}🔄 正在尝试启动控制台版本...{Style.RESET_ALL}")
        try:
            from main_console import main as console_main
            console_main()
        except Exception as console_error:
            print(f"{Fore.RED}❌ 控制台版本也启动失败: {console_error}{Style.RESET_ALL}")
            input("按回车键退出...")
        
    except Exception as e:
        error_msg = f"程序启动失败: {str(e)}"
        print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
        
        if splash:
            splash.close()
        
        try:
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        print(f"{Fore.YELLOW}🔄 正在尝试启动控制台版本...{Style.RESET_ALL}")
        try:
            from main_console import main as console_main
            console_main()
        except Exception as console_error:
            print(f"{Fore.RED}❌ 控制台版本也启动失败: {console_error}{Style.RESET_ALL}")
            input("按回车键退出...")

if __name__ == "__main__":
    main()
