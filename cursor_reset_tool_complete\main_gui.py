#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro 无限续杯工具 - 完整GUI版本
包含订单验证和所有功能的图形界面
"""

import sys
import os
import platform
from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont
from colorama import Fore, Style, init

# 初始化colorama
init()

def check_admin_privileges():
    """检查管理员权限"""
    if platform.system() == 'Windows':
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False
    return True

def request_admin_privileges():
    """请求管理员权限"""
    if platform.system() != 'Windows':
        return False
        
    try:
        import ctypes
        args = [sys.executable] + sys.argv
        
        # Request elevation via ShellExecute
        ctypes.windll.shell32.ShellExecuteW(None, "runas", args[0], " ".join('"' + arg + '"' for arg in args[1:]), None, 1)
        return True
    except Exception as e:
        print(f"无法以管理员权限重启: {e}")
        return False

def show_splash_screen(app):
    """显示启动画面"""
    splash = None
    try:
        # 尝试加载logo图片
        if os.path.exists("image/logo.png"):
            pixmap = QPixmap("image/logo.png")
            splash = QSplashScreen(pixmap)
        else:
            # 创建简单的启动画面
            pixmap = QPixmap(400, 300)
            pixmap.fill(Qt.GlobalColor.darkCyan)
            splash = QSplashScreen(pixmap)
        
        splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
        splash.show()
        
        # 显示加载信息
        splash.showMessage("正在启动 Cursor Pro 无限续杯工具...", 
                          Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, 
                          Qt.GlobalColor.white)
        
        app.processEvents()
        return splash
    except Exception as e:
        print(f"启动画面创建失败: {e}")
        return None

def check_dependencies():
    """检查依赖"""
    missing_deps = []
    
    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import colorama
    except ImportError:
        missing_deps.append("colorama")
    
    try:
        import psutil
    except ImportError:
        missing_deps.append("psutil")
    
    return missing_deps

def main():
    """主函数"""
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Cursor Pro 无限续杯工具 - GUI版本启动中...{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    # 检查依赖
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"{Fore.RED}❌ 缺少依赖包: {', '.join(missing_deps)}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}请运行: pip install {' '.join(missing_deps)}{Style.RESET_ALL}")
        input("按回车键退出...")
        return
    
    # 检查管理员权限（仅Windows）
    if platform.system() == 'Windows' and not check_admin_privileges():
        print(f"{Fore.YELLOW}⚠️  需要管理员权限{Style.RESET_ALL}")
        if request_admin_privileges():
            print(f"{Fore.CYAN}正在以管理员权限重启...{Style.RESET_ALL}")
            sys.exit(0)
        else:
            print(f"{Fore.YELLOW}⚠️  无法获取管理员权限，某些功能可能无法正常工作{Style.RESET_ALL}")
    
    # 创建QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Cursor Pro 无限续杯工具")
    app.setApplicationVersion("2.2.3")
    app.setOrganizationName("Pin Studios")
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 显示启动画面
    splash = show_splash_screen(app)
    
    try:
        # 延迟加载主要模块
        if splash:
            splash.showMessage("正在加载核心模块...", 
                              Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, 
                              Qt.GlobalColor.white)
            app.processEvents()
        
        # 导入GUI模块
        from ui_order_verify import show_order_verify_dialog
        from ui_main import MainWindow
        
        if splash:
            splash.showMessage("正在进行订单验证...", 
                              Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, 
                              Qt.GlobalColor.white)
            app.processEvents()
        
        # 首先进行订单验证
        print(f"{Fore.CYAN}📋 正在进行订单验证...{Style.RESET_ALL}")
        
        # 隐藏启动画面进行订单验证
        if splash:
            splash.hide()
        
        # 显示订单验证对话框
        order_verified = show_order_verify_dialog()
        
        if not order_verified:
            print(f"{Fore.RED}❌ 订单验证失败或被取消{Style.RESET_ALL}")
            QMessageBox.critical(None, "验证失败", "订单验证失败，程序将退出")
            return
        
        print(f"{Fore.GREEN}✅ 订单验证成功{Style.RESET_ALL}")
        
        # 重新显示启动画面
        if splash:
            splash.show()
            splash.showMessage("正在启动主界面...", 
                              Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, 
                              Qt.GlobalColor.white)
            app.processEvents()
        
        # 创建主窗口
        print(f"{Fore.CYAN}🚀 正在启动主界面...{Style.RESET_ALL}")
        main_window = MainWindow()
        
        # 隐藏启动画面，显示主窗口
        if splash:
            splash.finish(main_window)
        
        main_window.show()
        
        print(f"{Fore.GREEN}✅ GUI界面启动成功！{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except ImportError as e:
        if splash:
            splash.hide()
        
        error_msg = f"GUI模块导入失败: {str(e)}\n\n请确保已安装PyQt6:\npip install PyQt6"
        print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
        
        # 尝试显示错误对话框
        try:
            QMessageBox.critical(None, "导入错误", error_msg)
        except:
            pass
        
        # 回退到控制台版本
        print(f"{Fore.YELLOW}🔄 正在启动控制台版本...{Style.RESET_ALL}")
        try:
            from main_console import main as console_main
            console_main()
        except Exception as console_error:
            print(f"{Fore.RED}❌ 控制台版本也启动失败: {console_error}{Style.RESET_ALL}")
            input("按回车键退出...")
        
    except Exception as e:
        if splash:
            splash.hide()
        
        error_msg = f"程序启动失败: {str(e)}"
        print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
        
        try:
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        print(f"{Fore.YELLOW}🔄 正在启动控制台版本...{Style.RESET_ALL}")
        try:
            from main_console import main as console_main
            console_main()
        except Exception as console_error:
            print(f"{Fore.RED}❌ 控制台版本也启动失败: {console_error}{Style.RESET_ALL}")
            input("按回车键退出...")

if __name__ == "__main__":
    main()
