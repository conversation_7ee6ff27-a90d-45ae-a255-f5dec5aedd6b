#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI测试程序 - 简化版本
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                            QMessageBox, QTabWidget, QGroupBox)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPixmap, QIcon

class SimpleMainWindow(QMainWindow):
    """简化的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.apply_style()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("Cursor Pro 无限续杯工具 v2.2.3 - GUI测试版")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("🎯 Cursor Pro 无限续杯工具")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #00d4aa; margin: 20px;")
        main_layout.addWidget(title_label)
        
        # 版本信息
        version_label = QLabel("Pro Version Activator v2.2.3")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version_label.setStyleSheet("font-size: 16px; color: #888; margin-bottom: 20px;")
        main_layout.addWidget(version_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 主功能选项卡
        self.create_main_tab()
        
        # 关于选项卡
        self.create_about_tab()
        
        main_layout.addWidget(self.tab_widget)
        
        # 状态栏
        self.status_label = QLabel("GUI界面测试版本 - 就绪")
        self.status_label.setStyleSheet("color: #00d4aa; font-weight: bold; padding: 10px;")
        main_layout.addWidget(self.status_label)
    
    def create_main_tab(self):
        """创建主功能选项卡"""
        main_tab = QWidget()
        layout = QVBoxLayout(main_tab)
        
        # 功能按钮组
        buttons_group = QGroupBox("主要功能")
        buttons_layout = QVBoxLayout(buttons_group)
        
        # 按钮列表
        buttons = [
            ("🔄 重置 Cursor 机器 ID", self.reset_machine_id, "#00d4aa"),
            ("📧 邮箱管理工具", self.email_manager, "#0984e3"),
            ("⚙️ 关闭 Cursor 进程", self.quit_cursor, "#e17055"),
            ("🧹 清理状态数据库", self.clean_database, "#a29bfe"),
            ("💰 订单验证", self.verify_order, "#fdcb6e")
        ]
        
        for text, handler, color in buttons:
            btn = QPushButton(text)
            btn.setMinimumHeight(50)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    margin: 5px;
                }}
                QPushButton:hover {{
                    opacity: 0.8;
                }}
                QPushButton:pressed {{
                    opacity: 0.6;
                }}
            """)
            btn.clicked.connect(handler)
            buttons_layout.addWidget(btn)
        
        layout.addWidget(buttons_group)
        layout.addStretch()
        
        self.tab_widget.addTab(main_tab, "🏠 主要功能")
    
    def create_about_tab(self):
        """创建关于选项卡"""
        about_tab = QWidget()
        layout = QVBoxLayout(about_tab)
        
        about_text = QTextEdit()
        about_text.setReadOnly(True)
        about_text.setHtml("""
        <div style="text-align: center; padding: 20px; font-family: 'Microsoft YaHei';">
            <h2 style="color: #00d4aa;">🎯 Cursor Pro 无限续杯工具</h2>
            <h3 style="color: #888;">Pro Version Activator v2.2.3</h3>
            
            <p><strong>作者:</strong> Pin Studios (yeongpin)</p>
            <p><strong>GitHub:</strong> <a href="https://github.com/yeongpin/cursor-free-vip" style="color: #0984e3;">https://github.com/yeongpin/cursor-free-vip</a></p>
            
            <h4 style="color: #00d4aa;">✨ 功能特性:</h4>
            <ul style="text-align: left; max-width: 500px; margin: 0 auto;">
                <li>🔄 重置 Cursor 机器 ID</li>
                <li>📧 临时邮箱管理系统</li>
                <li>⚙️ 进程管理功能</li>
                <li>🧹 状态数据库清理</li>
                <li>💰 订单验证系统</li>
                <li>🎨 现代化GUI界面</li>
            </ul>
            
            <h4 style="color: #00d4aa;">🤝 贡献者:</h4>
            <p style="font-size: 12px; color: #666;">BasaiCorp, aliensb, handwerk2016, Nigel1992, UntaDotMy, RenjiYuusei, imbajin, ahmed98Osama, bingoohuang, mALIk-sHAHId, MFaiqKhan, httpmerak, muhammedfurkan, plamkatawe, Lucaszmv</p>
            
            <p style="color: #e74c3c; font-weight: bold; margin-top: 30px;">⚠️ 本工具仅供学习研究使用</p>
            <p style="color: #e74c3c; font-size: 12px;">请遵守相关法律法规和软件许可协议</p>
        </div>
        """)
        
        layout.addWidget(about_text)
        
        self.tab_widget.addTab(about_tab, "ℹ️ 关于")
    
    def apply_style(self):
        """应用深色主题"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            QWidget {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            QTabWidget::pane {
                border: 1px solid #34495e;
                background-color: #34495e;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #34495e;
                color: #ecf0f1;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #00d4aa;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #4a6741;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #34495e;
                border-radius: 12px;
                margin-top: 1ex;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #00d4aa;
                font-size: 16px;
            }
            QTextEdit {
                background-color: #34495e;
                border: 1px solid #4a6741;
                border-radius: 8px;
                padding: 10px;
                color: #ecf0f1;
            }
        """)
    
    def update_status(self, message):
        """更新状态"""
        self.status_label.setText(message)
    
    def reset_machine_id(self):
        """重置机器ID"""
        reply = QMessageBox.question(self, "确认操作", 
                                   "确定要重置Cursor机器ID吗？\n\n这将生成新的设备标识符。", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            self.update_status("🔄 正在重置机器ID...")
            try:
                # 这里调用实际的重置功能
                import reset_machine_manual
                
                class SimpleTranslator:
                    def get(self, key, **kwargs):
                        return key.format(**kwargs) if kwargs else key
                
                translator = SimpleTranslator()
                result = reset_machine_manual.run(translator)
                
                if result:
                    self.update_status("✅ 机器ID重置成功")
                    QMessageBox.information(self, "成功", "机器ID重置成功！")
                else:
                    self.update_status("❌ 机器ID重置失败")
                    QMessageBox.warning(self, "失败", "机器ID重置失败")
                    
            except Exception as e:
                self.update_status(f"❌ 重置错误: {str(e)}")
                QMessageBox.critical(self, "错误", f"重置过程出错:\n{str(e)}")
    
    def email_manager(self):
        """邮箱管理"""
        self.update_status("📧 正在启动邮箱管理器...")
        try:
            import email_manager
            manager = email_manager.EmailManager()
            email = manager.generate_sub_email()
            
            self.update_status(f"✅ 临时邮箱已生成: {email}")
            QMessageBox.information(self, "邮箱管理", f"临时邮箱已生成:\n{email}\n\n邮箱管理器已在后台运行")
            
        except Exception as e:
            self.update_status(f"❌ 邮箱管理器错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"邮箱管理器错误:\n{str(e)}")
    
    def quit_cursor(self):
        """关闭Cursor进程"""
        reply = QMessageBox.question(self, "确认操作", 
                                   "确定要关闭所有Cursor进程吗？", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            self.update_status("⚙️ 正在关闭Cursor进程...")
            try:
                import quit_cursor
                
                class SimpleTranslator:
                    def get(self, key, **kwargs):
                        return key.format(**kwargs) if kwargs else key
                
                translator = SimpleTranslator()
                quit_cursor.quit_cursor(translator)
                
                self.update_status("✅ Cursor进程已关闭")
                QMessageBox.information(self, "成功", "Cursor进程已关闭")
                
            except Exception as e:
                self.update_status(f"❌ 进程关闭错误: {str(e)}")
                QMessageBox.critical(self, "错误", f"进程关闭错误:\n{str(e)}")
    
    def clean_database(self):
        """清理数据库"""
        reply = QMessageBox.question(self, "确认操作", 
                                   "确定要清理Cursor状态数据库吗？\n\n这将删除相关的状态文件。", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            self.update_status("🧹 正在清理状态数据库...")
            try:
                import delete_state_db
                delete_state_db.delete_state_db()
                
                self.update_status("✅ 状态数据库已清理")
                QMessageBox.information(self, "成功", "状态数据库已清理")
                
            except Exception as e:
                self.update_status(f"❌ 数据库清理错误: {str(e)}")
                QMessageBox.critical(self, "错误", f"数据库清理错误:\n{str(e)}")
    
    def verify_order(self):
        """订单验证"""
        self.update_status("💰 正在启动订单验证...")
        try:
            from ui_order_verify import show_order_verify_dialog
            success = show_order_verify_dialog(self)
            
            if success:
                self.update_status("✅ 订单验证成功")
            else:
                self.update_status("订单验证已取消")
                
        except Exception as e:
            self.update_status(f"❌ 订单验证错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"订单验证错误:\n{str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("Cursor Pro 无限续杯工具")
    app.setApplicationVersion("2.2.3")
    app.setOrganizationName("Pin Studios")
    
    # 设置字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建主窗口
    window = SimpleMainWindow()
    window.show()
    
    print("🎉 GUI测试版本启动成功！")
    print("窗口已显示，请在GUI界面中操作")
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
