#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的2925邮箱服务测试
"""

import socket
import ssl

def test_pop3_connection():
    """测试POP3服务器连接"""
    server = 'pop.2925.com'
    port = 995
    
    print(f"🔄 测试连接到 {server}:{port}")
    
    try:
        # 创建SSL上下文
        context = ssl.create_default_context()
        
        # 创建socket连接
        with socket.create_connection((server, port), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname=server) as ssock:
                print("✅ SSL连接成功")
                
                # 读取服务器欢迎消息
                response = ssock.recv(1024).decode('utf-8', errors='ignore')
                print(f"📧 服务器响应: {response.strip()}")
                
                return True
                
    except socket.timeout:
        print("❌ 连接超时")
        return False
    except socket.gaierror as e:
        print(f"❌ DNS解析失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def test_domain_resolution():
    """测试域名解析"""
    server = 'pop.2925.com'
    
    print(f"🔄 测试域名解析: {server}")
    
    try:
        import socket
        ip = socket.gethostbyname(server)
        print(f"✅ 域名解析成功: {server} -> {ip}")
        return True
    except Exception as e:
        print(f"❌ 域名解析失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 2925邮箱服务简单连接测试")
    print("="*50)
    
    # 测试域名解析
    dns_ok = test_domain_resolution()
    
    if dns_ok:
        # 测试POP3连接
        pop3_ok = test_pop3_connection()
        
        if pop3_ok:
            print("\n🎉 基础连接测试通过！")
            print("💡 建议：2925邮箱服务的POP3服务器可以连接")
            print("💡 如果验证码获取仍有问题，可能是:")
            print("   1. 邮箱账号密码问题")
            print("   2. 邮件匹配逻辑问题")
            print("   3. 验证码提取逻辑问题")
        else:
            print("\n❌ POP3连接失败")
            print("💡 可能的原因:")
            print("   1. 防火墙阻止连接")
            print("   2. 网络环境限制")
            print("   3. 服务器暂时不可用")
    else:
        print("\n❌ 域名解析失败")
        print("💡 可能的原因:")
        print("   1. DNS服务器问题")
        print("   2. 网络连接问题")
        print("   3. 域名不存在或已更改")
    
    print("\n" + "="*50)

if __name__ == "__main__":
    main()
