# main_integrated.py - 完整集成版Cursor无限续杯工具
import os
import sys
import platform
from colorama import Fore, Style, init
import locale
from config import get_config, force_update_config
from utils import get_user_documents_path

# Initialize colorama
init()

# Define emoji and color constants
EMOJI = {
    "FILE": "📄",
    "BACKUP": "💾",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "INFO": "ℹ️",
    "RESET": "🔄",
    "MENU": "📋",
    "ARROW": "➜",
    "LANG": "🌐",
    "ADMIN": "🔐",
    "EMAIL": "📧",
    "PROCESS": "⚙️",
    "CLEAN": "🧹",
    "ORDER": "💰"
}

# Function to check if running as frozen executable
def is_frozen():
    """Check if the script is running as a frozen executable."""
    return getattr(sys, 'frozen', False)

# Function to check admin privileges (Windows only)
def is_admin():
    """Check if the script is running with admin privileges (Windows only)."""
    if platform.system() == 'Windows':
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False
    # Always return True for non-Windows to avoid changing behavior
    return True

# Function to restart with admin privileges
def run_as_admin():
    """Restart the current script with admin privileges (Windows only)."""
    if platform.system() != 'Windows':
        return False
        
    try:
        import ctypes
        args = [sys.executable] + sys.argv
        
        # Request elevation via ShellExecute
        print(f"{Fore.YELLOW}{EMOJI['ADMIN']} 请求管理员权限...{Style.RESET_ALL}")
        ctypes.windll.shell32.ShellExecuteW(None, "runas", args[0], " ".join('"' + arg + '"' for arg in args[1:]), None, 1)
        return True
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 无法以管理员权限重启: {e}{Style.RESET_ALL}")
        return False

class Translator:
    def __init__(self):
        self.translations = {}
        self.current_language = 'zh_cn'  # 默认中文
        self.fallback_language = 'en'

    def detect_system_language(self):
        """Detect system language and return corresponding language code"""
        try:
            # 简化的语言检测
            import locale
            locale.setlocale(locale.LC_ALL, '')
            system_locale = locale.getlocale()[0]
            if system_locale and 'chinese' in system_locale.lower():
                return 'zh_cn'
            return 'en'
        except:
            return 'zh_cn'  # 默认中文

    def get(self, key, **kwargs):
        """Get translated text with fallback support"""
        try:
            # For simplicity, just return the key since we're removing most functionality
            return key.format(**kwargs) if kwargs else key
        except Exception:
            return key

# Create translator instance
translator = Translator()

def print_menu():
    """Print menu options"""
    print(f"\n{Fore.CYAN}{EMOJI['MENU']} 菜单:{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}{'─' * 70}{Style.RESET_ALL}")
    
    print(f"{Fore.GREEN}1{Style.RESET_ALL}. {EMOJI['RESET']} 重置 Cursor 机器 ID")
    print(f"{Fore.GREEN}2{Style.RESET_ALL}. {EMOJI['EMAIL']} 邮箱管理工具")
    print(f"{Fore.GREEN}3{Style.RESET_ALL}. {EMOJI['PROCESS']} 关闭 Cursor 进程")
    print(f"{Fore.GREEN}4{Style.RESET_ALL}. {EMOJI['CLEAN']} 清理状态数据库")
    print(f"{Fore.GREEN}5{Style.RESET_ALL}. {EMOJI['ORDER']} 订单验证")
    print(f"{Fore.GREEN}0{Style.RESET_ALL}. {EMOJI['ERROR']} 退出")
    
    print(f"{Fore.YELLOW}{'─' * 70}{Style.RESET_ALL}")

def run_order_verification():
    """运行订单验证"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在启动订单验证...{Style.RESET_ALL}")
        import verify_order
        result = verify_order.main_verification_window()
        if result:
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 订单验证成功{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}{EMOJI['ERROR']} 订单验证失败{Style.RESET_ALL}")
        return result
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 订单验证模块错误: {e}{Style.RESET_ALL}")
        return False

def run_machine_reset():
    """运行机器ID重置"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在启动机器ID重置...{Style.RESET_ALL}")
        import reset_machine_manual
        reset_machine_manual.run(translator)
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 机器ID重置错误: {e}{Style.RESET_ALL}")

def run_email_manager():
    """运行邮箱管理器"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在启动邮箱管理器...{Style.RESET_ALL}")
        import email_manager
        email_manager.main()
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 邮箱管理器错误: {e}{Style.RESET_ALL}")

def run_quit_cursor():
    """运行Cursor进程关闭"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在关闭Cursor进程...{Style.RESET_ALL}")
        import quit_cursor
        quit_cursor.quit_cursor(translator)
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 进程关闭错误: {e}{Style.RESET_ALL}")

def run_delete_state():
    """运行状态数据库清理"""
    try:
        print(f"{Fore.CYAN}{EMOJI['INFO']} 正在清理状态数据库...{Style.RESET_ALL}")
        import delete_state_db
        delete_state_db.delete_state_db()
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 状态清理错误: {e}{Style.RESET_ALL}")

def main():
    # Check for admin privileges if running as executable on Windows only
    if platform.system() == 'Windows' and is_frozen() and not is_admin():
        print(f"{Fore.YELLOW}{EMOJI['ADMIN']} 需要管理员权限{Style.RESET_ALL}")
        if run_as_admin():
            sys.exit(0)  # Exit after requesting admin privileges
        else:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 需要管理员权限才能继续{Style.RESET_ALL}")
    
    # 导入 logo 模块
    try:
        from logo import print_logo
        print_logo()
    except ImportError:
        print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Cursor 无限续杯工具 - 完整版{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
    
    # 首次启动时进行订单验证
    print(f"{Fore.CYAN}{EMOJI['INFO']} 正在进行订单验证...{Style.RESET_ALL}")
    if not run_order_verification():
        print(f"{Fore.YELLOW}{EMOJI['INFO']} 跳过订单验证，继续使用程序{Style.RESET_ALL}")
    
    # Initialize configuration
    try:
        config = get_config(translator)
        if not config:
            print(f"{Fore.RED}{EMOJI['ERROR']} 配置初始化失败{Style.RESET_ALL}")
            return
        force_update_config(translator)
    except Exception as e:
        print(f"{Fore.YELLOW}{EMOJI['INFO']} 配置初始化警告: {e}{Style.RESET_ALL}")

    print_menu()
    
    while True:
        try:
            choice = input(f"\n{EMOJI['ARROW']} {Fore.CYAN}请输入选择 (0-5): {Style.RESET_ALL}")

            if choice == "0":
                print(f"\n{Fore.YELLOW}{EMOJI['INFO']} 退出中...{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
                return
            elif choice == "1":
                run_machine_reset()
                print_menu()
            elif choice == "2":
                run_email_manager()
                print_menu()
            elif choice == "3":
                run_quit_cursor()
                print_menu()
            elif choice == "4":
                run_delete_state()
                print_menu()
            elif choice == "5":
                run_order_verification()
                print_menu()
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} 无效的选择，请输入0-5{Style.RESET_ALL}")
                print_menu()

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}{EMOJI['INFO']} 程序已终止{Style.RESET_ALL}")
            print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
            return
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 发生错误: {str(e)}{Style.RESET_ALL}")
            print_menu()

if __name__ == "__main__":
    main()
