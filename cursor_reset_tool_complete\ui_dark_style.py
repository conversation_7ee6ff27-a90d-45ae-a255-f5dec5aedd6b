#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深色主题风格的GUI界面 - 仿照原始设计
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFrame, 
                            QMessageBox, QProgressBar, QLineEdit, QDialog,
                            QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QPixmap, QIcon, QPalette, QColor, QPainter, QLinearGradient

class WorkerThread(QThread):
    """后台工作线程"""
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    
    def __init__(self, task_type):
        super().__init__()
        self.task_type = task_type
    
    def run(self):
        try:
            if self.task_type == "reset_machine":
                self.reset_machine_id()
            elif self.task_type == "email_manager":
                self.run_email_manager()
            elif self.task_type == "quit_cursor":
                self.quit_cursor_process()
            elif self.task_type == "delete_state":
                self.delete_state_db()
        except Exception as e:
            self.finished.emit(False, str(e))
    
    def reset_machine_id(self):
        """重置机器ID"""
        self.message.emit("正在重置机器ID...")
        self.progress.emit(30)
        
        try:
            import reset_machine_manual
            
            class SimpleTranslator:
                def get(self, key, **kwargs):
                    return key.format(**kwargs) if kwargs else key
            
            translator = SimpleTranslator()
            self.progress.emit(70)
            
            result = reset_machine_manual.run(translator)
            self.progress.emit(100)
            
            if result:
                self.finished.emit(True, "机器ID重置成功！")
            else:
                self.finished.emit(False, "机器ID重置失败")
        except Exception as e:
            self.finished.emit(False, f"重置过程出错: {str(e)}")
    
    def run_email_manager(self):
        """运行邮箱管理器"""
        self.message.emit("正在启动邮箱管理器...")
        self.progress.emit(40)
        
        try:
            import email_manager
            manager = email_manager.EmailManager()
            self.progress.emit(80)
            
            email = manager.generate_sub_email()
            self.progress.emit(100)
            self.finished.emit(True, f"临时邮箱已生成: {email}")
        except Exception as e:
            self.finished.emit(False, f"邮箱管理器错误: {str(e)}")
    
    def quit_cursor_process(self):
        """关闭Cursor进程"""
        self.message.emit("正在关闭Cursor进程...")
        self.progress.emit(50)
        
        try:
            import quit_cursor
            
            class SimpleTranslator:
                def get(self, key, **kwargs):
                    return key.format(**kwargs) if kwargs else key
            
            translator = SimpleTranslator()
            self.progress.emit(90)
            
            quit_cursor.quit_cursor(translator)
            self.progress.emit(100)
            self.finished.emit(True, "Cursor进程已关闭")
        except Exception as e:
            self.finished.emit(False, f"关闭进程错误: {str(e)}")
    
    def delete_state_db(self):
        """删除状态数据库"""
        self.message.emit("正在清理状态数据库...")
        self.progress.emit(50)
        
        try:
            import delete_state_db
            self.progress.emit(90)
            
            delete_state_db.delete_state_db()
            self.progress.emit(100)
            self.finished.emit(True, "状态数据库已清理")
        except Exception as e:
            self.finished.emit(False, f"清理数据库错误: {str(e)}")

class DarkButton(QPushButton):
    """深色主题按钮"""
    
    def __init__(self, text, color_type="orange"):
        super().__init__(text)
        self.color_type = color_type
        self.setup_style()
    
    def setup_style(self):
        """设置按钮样式"""
        self.setMinimumHeight(50)
        self.setMinimumWidth(280)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 根据颜色类型设置不同的样式
        if self.color_type == "orange":
            border_color = "#ff6b35"
            hover_bg = "rgba(255, 107, 53, 0.1)"
        elif self.color_type == "cyan":
            border_color = "#00d4aa"
            hover_bg = "rgba(0, 212, 170, 0.1)"
        elif self.color_type == "purple":
            border_color = "#8b5cf6"
            hover_bg = "rgba(139, 92, 246, 0.1)"
        else:
            border_color = "#6b7280"
            hover_bg = "rgba(107, 114, 128, 0.1)"
        
        self.setStyleSheet(f"""
            DarkButton {{
                background: transparent;
                border: 2px solid {border_color};
                border-radius: 8px;
                color: white;
                font-size: 16px;
                font-weight: 500;
                padding: 12px 20px;
                font-family: 'Microsoft YaHei', sans-serif;
            }}
            DarkButton:hover {{
                background: {hover_bg};
                border-color: {border_color};
            }}
            DarkButton:pressed {{
                background: rgba(255, 255, 255, 0.05);
            }}
        """)

class EmailManagerDialog(QDialog):
    """邮箱管理器对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.apply_style()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("邮箱管理器")
        self.setFixedSize(400, 350)
        self.setModal(True)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("emailContainer")
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)
        
        # 内容布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 标题栏
        title_layout = QHBoxLayout()
        title_icon = QLabel("📧")
        title_icon.setStyleSheet("font-size: 20px;")
        title_label = QLabel("邮箱管理器")
        title_label.setObjectName("emailTitle")
        
        close_btn = QPushButton("✕")
        close_btn.setObjectName("closeBtn")
        close_btn.setFixedSize(25, 25)
        close_btn.clicked.connect(self.close)
        
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(close_btn)
        
        # 邮箱验证工具标题
        tool_title = QLabel("邮箱验证工具")
        tool_title.setObjectName("toolTitle")
        tool_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 分割线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setObjectName("purpleLine")
        
        # 邮箱输入框
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("正在生成邮箱...")
        self.email_input.setObjectName("emailInput")
        self.email_input.setReadOnly(True)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        domain_btn = DarkButton("域名邮箱", "orange")
        domain_btn.clicked.connect(self.generate_domain_email)
        
        temp_btn = DarkButton("无限邮箱", "purple")
        temp_btn.clicked.connect(self.generate_temp_email)
        
        button_layout.addWidget(domain_btn)
        button_layout.addWidget(temp_btn)
        
        # 获取验证码按钮
        verify_btn = DarkButton("获取验证码", "cyan")
        verify_btn.clicked.connect(self.get_verification_code)
        
        layout.addLayout(title_layout)
        layout.addWidget(tool_title)
        layout.addWidget(line)
        layout.addWidget(self.email_input)
        layout.addLayout(button_layout)
        layout.addWidget(verify_btn)
        
        # 自动生成邮箱
        self.generate_domain_email()
    
    def apply_style(self):
        """应用样式"""
        self.setStyleSheet("""
            #emailContainer {
                background: #1a1a1a;
                border: 1px solid #333;
                border-radius: 10px;
            }
            
            #emailTitle {
                color: white;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #closeBtn {
                background: #ff5f56;
                border: none;
                border-radius: 12px;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }
            
            #closeBtn:hover {
                background: #ff3b30;
            }
            
            #toolTitle {
                color: white;
                font-size: 18px;
                font-weight: 600;
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 10px 0;
            }
            
            #purpleLine {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 #8b5cf6, stop:1 transparent);
                border: none;
                height: 2px;
                margin: 5px 0;
            }
            
            #emailInput {
                background: #2a2a2a;
                border: 1px solid #444;
                border-radius: 6px;
                color: #00d4aa;
                font-size: 14px;
                font-family: 'Consolas', monospace;
                padding: 12px 15px;
                min-height: 20px;
            }
            
            #emailInput:focus {
                border-color: #00d4aa;
            }
        """)
    
    def generate_domain_email(self):
        """生成域名邮箱"""
        try:
            import email_manager
            manager = email_manager.EmailManager()
            email = manager.generate_sub_email()
            self.email_input.setText(email)
        except Exception as e:
            self.email_input.setText(f"生成失败: {str(e)}")
    
    def generate_temp_email(self):
        """生成临时邮箱"""
        try:
            import email_manager
            manager = email_manager.EmailManager()
            email = manager.generate_sub_email()
            self.email_input.setText(email)
        except Exception as e:
            self.email_input.setText(f"生成失败: {str(e)}")
    
    def get_verification_code(self):
        """获取验证码"""
        email = self.email_input.text()
        if email and "@" in email:
            QMessageBox.information(self, "提示", f"正在监控邮箱 {email} 的验证码...")
            # 这里可以启动验证码监控
        else:
            QMessageBox.warning(self, "错误", "请先生成有效的邮箱地址")

class DarkMainWindow(QMainWindow):
    """深色主题主窗口"""
    
    def __init__(self):
        super().__init__()
        self.worker_thread = None
        self.init_ui()
        self.apply_dark_style()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("Cursor Free VIP")
        self.setFixedSize(350, 400)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 创建主容器
        main_container = QWidget()
        main_container.setObjectName("mainContainer")
        self.setCentralWidget(main_container)
        
        # 主布局
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 创建标题栏
        self.create_title_bar(main_layout)
        
        # 创建内容区域
        self.create_content_area(main_layout)
        
        # 添加窗口阴影
        self.add_window_shadow()
    
    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_layout = QHBoxLayout()
        
        # Logo和版本
        logo_label = QLabel("🎯")
        logo_label.setStyleSheet("font-size: 16px;")
        
        version_label = QLabel("v2.2.3")
        version_label.setObjectName("versionLabel")
        
        # 窗口控制按钮
        control_layout = QHBoxLayout()
        control_layout.setSpacing(5)
        
        minimize_btn = QPushButton("—")
        minimize_btn.setObjectName("controlBtn")
        minimize_btn.setFixedSize(20, 20)
        minimize_btn.clicked.connect(self.showMinimized)
        
        maximize_btn = QPushButton("□")
        maximize_btn.setObjectName("controlBtn")
        maximize_btn.setFixedSize(20, 20)
        # maximize_btn.clicked.connect(self.toggle_maximize)
        
        close_btn = QPushButton("✕")
        close_btn.setObjectName("closeBtn")
        close_btn.setFixedSize(20, 20)
        close_btn.clicked.connect(self.close)
        
        control_layout.addWidget(minimize_btn)
        control_layout.addWidget(maximize_btn)
        control_layout.addWidget(close_btn)
        
        title_layout.addWidget(logo_label)
        title_layout.addWidget(version_label)
        title_layout.addStretch()
        title_layout.addLayout(control_layout)
        
        parent_layout.addLayout(title_layout)
        
        # 使标题栏可拖拽
        self.title_widget = QWidget()
        self.title_widget.setLayout(title_layout)
        self.title_widget.mousePressEvent = self.mouse_press_event
        self.title_widget.mouseMoveEvent = self.mouse_move_event
    
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        # 主标题
        main_title = QLabel("Cursor Free VIP")
        main_title.setObjectName("mainTitle")
        main_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 分割线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setObjectName("gradientLine")
        
        # 副标题
        subtitle = QLabel("机器ID已重置")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 另一条分割线
        line2 = QFrame()
        line2.setFrameShape(QFrame.Shape.HLine)
        line2.setObjectName("gradientLine")
        
        # 功能按钮
        self.unlimited_btn = DarkButton("无限续杯", "orange")
        self.unlimited_btn.clicked.connect(self.handle_unlimited)
        
        self.reset_btn = DarkButton("重置Cursor", "cyan")
        self.reset_btn.clicked.connect(self.handle_reset_cursor)
        
        # 邮箱管理按钮
        self.email_btn = DarkButton("邮箱管理", "purple")
        self.email_btn.clicked.connect(self.handle_email_manager)
        
        parent_layout.addWidget(main_title)
        parent_layout.addWidget(line)
        parent_layout.addWidget(subtitle)
        parent_layout.addWidget(line2)
        parent_layout.addWidget(self.unlimited_btn)
        parent_layout.addWidget(self.reset_btn)
        parent_layout.addWidget(self.email_btn)
        parent_layout.addStretch()
    
    def add_window_shadow(self):
        """添加窗口阴影"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 160))
        shadow.setOffset(0, 5)
        self.centralWidget().setGraphicsEffect(shadow)
    
    def apply_dark_style(self):
        """应用深色样式"""
        self.setStyleSheet("""
            #mainContainer {
                background: #1a1a1a;
                border: 1px solid #333;
                border-radius: 10px;
            }
            
            #versionLabel {
                color: #888;
                font-size: 12px;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #controlBtn {
                background: #444;
                border: none;
                border-radius: 3px;
                color: #ccc;
                font-size: 10px;
            }
            
            #controlBtn:hover {
                background: #555;
            }
            
            #closeBtn {
                background: #ff5f56;
                border: none;
                border-radius: 3px;
                color: white;
                font-size: 10px;
            }
            
            #closeBtn:hover {
                background: #ff3b30;
            }
            
            #mainTitle {
                color: #8b5cf6;
                font-size: 20px;
                font-weight: 600;
                font-family: 'Microsoft YaHei', sans-serif;
                border: 2px solid #8b5cf6;
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
            }
            
            #subtitle {
                color: white;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 10px 0;
            }
            
            #gradientLine {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 #8b5cf6, stop:1 transparent);
                border: none;
                height: 2px;
                margin: 5px 0;
            }
        """)
    
    def mouse_press_event(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouse_move_event(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def handle_unlimited(self):
        """处理无限续杯"""
        self.run_task("reset_machine", "正在执行无限续杯...")
    
    def handle_reset_cursor(self):
        """处理重置Cursor"""
        reply = QMessageBox.question(self, "确认", "确定要重置Cursor吗？", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.run_task("quit_cursor", "正在重置Cursor...")
    
    def handle_email_manager(self):
        """处理邮箱管理"""
        dialog = EmailManagerDialog(self)
        dialog.exec()
    
    def run_task(self, task_type, message):
        """运行任务"""
        # 禁用按钮
        self.unlimited_btn.setEnabled(False)
        self.reset_btn.setEnabled(False)
        self.email_btn.setEnabled(False)
        
        # 启动工作线程
        self.worker_thread = WorkerThread(task_type)
        self.worker_thread.finished.connect(self.on_task_finished)
        self.worker_thread.start()
    
    def on_task_finished(self, success, message):
        """任务完成回调"""
        # 重新启用按钮
        self.unlimited_btn.setEnabled(True)
        self.reset_btn.setEnabled(True)
        self.email_btn.setEnabled(True)
        
        # 显示结果
        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "错误", message)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("Cursor Free VIP")
    app.setApplicationVersion("2.2.3")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建并显示主窗口
    window = DarkMainWindow()
    
    # 居中显示
    screen = app.primaryScreen().geometry()
    window.move(
        (screen.width() - window.width()) // 2,
        (screen.height() - window.height()) // 2
    )
    
    window.show()
    
    print("🎨 深色主题GUI界面启动成功！")
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
