import os
import sys
import configparser
from colorama import Fore, Style
from utils import get_user_documents_path, get_linux_cursor_path

EMOJI = {
    "INFO": "ℹ️",
    "WARNING": "⚠️",
    "ERROR": "❌",
    "SUCCESS": "✅",
    "ADMIN": "🔒",
}

# global config cache
_config_cache = None

def find_cursor_path_from_env():
    """尝试从环境变量中获取Cursor路径"""
    cursor_env = os.getenv("CURSOR_PATH")
    if cursor_env and os.path.exists(cursor_env):
        print(f"{Fore.CYAN}{EMOJI['INFO']} 从环境变量找到Cursor路径: {cursor_env}{Style.RESET_ALL}")
        return cursor_env
    return None

def find_cursor_path_from_process():
    """尝试从运行的进程中获取Cursor路径"""
    try:
        import psutil
        for proc in psutil.process_iter(['name', 'exe']):
            if proc.info['name'] and 'cursor' in proc.info['name'].lower():
                if proc.info['exe']:
                    # 从进程路径获取Cursor安装目录
                    cursor_dir = os.path.dirname(proc.info['exe'])
                    resources_app = os.path.join(cursor_dir, "resources", "app")
                    if os.path.exists(resources_app):
                        print(f"{Fore.CYAN}{EMOJI['INFO']} 从进程找到Cursor路径: {resources_app}{Style.RESET_ALL}")
                        return resources_app
    except (ImportError, Exception) as e:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 无法从进程检查Cursor路径: {str(e)}{Style.RESET_ALL}")
    return None

def find_cursor_path_windows():
    """在Windows系统上查找Cursor安装路径"""
    # 默认路径
    localappdata = os.getenv("LOCALAPPDATA", "")
    default_path = os.path.join(localappdata, "Programs", "Cursor", "resources", "app")
    
    # 尝试从环境变量获取
    env_path = find_cursor_path_from_env()
    if env_path:
        return env_path
        
    # 尝试从进程获取
    proc_path = find_cursor_path_from_process()
    if proc_path:
        return proc_path

            
    # 如果所有方法都失败，返回默认路径并发出警告
    print(f"{Fore.YELLOW}{EMOJI['WARNING']} 未找到Cursor安装路径，使用默认路径: {default_path}{Style.RESET_ALL}")
    return default_path

def remove_readonly_attribute(file_path):
    """移除文件的只读属性（仅Windows系统）"""
    if sys.platform == "win32" and os.path.exists(file_path):
        try:
            import stat
            current_mode = os.stat(file_path).st_mode
            # 添加写权限
            os.chmod(file_path, current_mode | stat.S_IWRITE)
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 已移除文件只读属性: {file_path}{Style.RESET_ALL}")
            return True
        except Exception as e:
            print(f"{Fore.YELLOW}{EMOJI['WARNING']} 无法移除文件只读属性: {file_path} (错误: {str(e)}){Style.RESET_ALL}")
    return False

def setup_config(translator=None):
    """Setup configuration file and return config object"""
    try:
        # 根据操作系统选择合适的配置目录
        if sys.platform == "win32":
            # Windows系统使用C盘根目录
            docs_path = "C:\\"
        elif sys.platform == "darwin":
            # macOS系统使用用户主目录
            docs_path = os.path.expanduser("~")
        else:
            # Linux系统使用用户主目录
            docs_path = os.path.expanduser("~")
        
        # normalize path
        config_dir = os.path.normpath(os.path.join(docs_path, ".cursor-free-vip"))
        config_file = os.path.normpath(os.path.join(config_dir, "config.ini"))
        
        # create config directory, only print message when directory not exists
        dir_exists = os.path.exists(config_dir)
        try:
            os.makedirs(config_dir, exist_ok=True)
            if not dir_exists:  # only print message when directory not exists
                print(f"{Fore.CYAN}{EMOJI['INFO']} 配置目录已创建: {config_dir}{Style.RESET_ALL}")
        except Exception as e:
            # if cannot create directory, use temporary directory
            import tempfile
            temp_dir = os.path.normpath(os.path.join(tempfile.gettempdir(), ".cursor-free-vip"))
            temp_exists = os.path.exists(temp_dir)
            config_dir = temp_dir
            config_file = os.path.normpath(os.path.join(config_dir, "config.ini"))
            os.makedirs(config_dir, exist_ok=True)
            if not temp_exists:  # only print message when temporary directory not exists
                print(f"{Fore.YELLOW}{EMOJI['WARNING']} 使用临时目录: {config_dir} (错误: {str(e)}){Style.RESET_ALL}")
        
        # create config object
        config = configparser.ConfigParser()
        
        # Default configuration - 只保留路径相关配置
        default_config = {
            'Language': {
                'current_language': '',
                'fallback_language': 'en',
                'language_cache_dir': os.path.join(config_dir, "language_cache")
            }
        }

        # Add system-specific path configuration
        if sys.platform == "win32":
            appdata = os.getenv("APPDATA")
            localappdata = os.getenv("LOCALAPPDATA", "")
            
            # 使用智能检测函数找到cursor_path
            cursor_path = find_cursor_path_windows()
            
            storage_path = os.path.join(appdata, "Cursor", "User", "globalStorage", "storage.json")
            default_config['WindowsPaths'] = {
                'storage_path': storage_path,
                'sqlite_path': os.path.join(appdata, "Cursor", "User", "globalStorage", "state.vscdb"),
                'machine_id_path': os.path.join(appdata, "Cursor", "machineId"),
                'cursor_path': cursor_path,
            }
            # Create storage directory
            os.makedirs(os.path.dirname(storage_path), exist_ok=True)
            
            # 移除storage.json文件的只读属性
            if os.path.exists(storage_path):
                remove_readonly_attribute(storage_path)
            
        elif sys.platform == "darwin":
            default_config['MacPaths'] = {
                'storage_path': os.path.abspath(os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/storage.json")),
                'sqlite_path': os.path.abspath(os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/state.vscdb")),
                'machine_id_path': os.path.expanduser("~/Library/Application Support/Cursor/machineId"),
                'cursor_path': "/Applications/Cursor.app/Contents/Resources/app",
            }
            # Create storage directory
            os.makedirs(os.path.dirname(default_config['MacPaths']['storage_path']), exist_ok=True)
            
        elif sys.platform == "linux":
            # Get the actual user's home directory, handling both sudo and normal cases
            sudo_user = os.environ.get('SUDO_USER')
            current_user = sudo_user if sudo_user else (os.getenv('USER') or os.getenv('USERNAME'))
            
            if not current_user:
                current_user = os.path.expanduser('~').split('/')[-1]
            
            # Handle sudo case
            if sudo_user:
                actual_home = f"/home/<USER>"
            else:
                actual_home = f"/home/<USER>"
            
            if not os.path.exists(actual_home):
                actual_home = os.path.expanduser("~")
            
            # Define base config directory
            config_base = os.path.join(actual_home, ".config")
            cursor_dir = os.path.join(config_base, "cursor")
            
            # Define all paths using the found cursor directory
            default_config['LinuxPaths'] = {
                                'storage_path': os.path.abspath(os.path.join(cursor_dir, "User/globalStorage/storage.json")),
                'sqlite_path': os.path.abspath(os.path.join(cursor_dir, "User/globalStorage/state.vscdb")),
                'machine_id_path': os.path.join(cursor_dir, "machineid"),
                'cursor_path': get_linux_cursor_path()
            }

        # Read existing configuration and merge
        if os.path.exists(config_file):
            config.read(config_file, encoding='utf-8')
            config_modified = False
            
            for section, options in default_config.items():
                if not config.has_section(section):
                    config.add_section(section)
                    config_modified = True
                for option, value in options.items():
                    # 对于cursor_path，如果找到了新路径，始终更新
                    if option == 'cursor_path' and value and os.path.exists(value):
                        current_path = config.get(section, option, fallback='')
                        if current_path != value:
                            config.set(section, option, str(value))
                            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 更新Cursor路径: {value}{Style.RESET_ALL}")
                            config_modified = True
                    # 对于其他选项，只在不存在时添加
                    elif not config.has_option(section, option):
                        config.set(section, option, str(value))
                        config_modified = True

            if config_modified:
                with open(config_file, 'w', encoding='utf-8') as f:
                    config.write(f)
                print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 配置已更新{Style.RESET_ALL}")
        else:
            for section, options in default_config.items():
                config.add_section(section)
                for option, value in options.items():
                    config.set(section, option, str(value))

            with open(config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 配置已创建: {config_file}{Style.RESET_ALL}")

        return config

    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 配置设置错误: {str(e)}{Style.RESET_ALL}")
        return None
    
def print_config(config, translator=None):
    """Print configuration in a readable format"""
    if not config:
        print(f"{Fore.YELLOW}{EMOJI['WARNING']} 配置不可用{Style.RESET_ALL}")
        return
        
    print(f"\n{Fore.CYAN}{EMOJI['INFO']} 配置:{Style.RESET_ALL}")
    print(f"\n{Fore.CYAN}{'─' * 70}{Style.RESET_ALL}")
    for section in config.sections():
        print(f"{Fore.GREEN}[{section}]{Style.RESET_ALL}")
        for key, value in config.items(section):
            print(f"  {key} = {value}")
    
    print(f"\n{Fore.CYAN}{'─' * 70}{Style.RESET_ALL}")
    # 根据操作系统选择正确的配置文件路径
    if sys.platform == "win32":
        config_dir = "C:\\.cursor-free-vip\\config.ini"
    elif sys.platform == "darwin":
        config_dir = os.path.join(os.path.expanduser("~"), ".cursor-free-vip", "config.ini")
    else:  # Linux
        config_dir = os.path.join(os.path.expanduser("~"), ".cursor-free-vip", "config.ini")
    print(f"{Fore.CYAN}{EMOJI['INFO']} 配置目录: {config_dir}{Style.RESET_ALL}")
    print()  

def force_update_config(translator=None):
    """简化版本的强制更新配置文件功能"""
    return setup_config(translator)

def get_config(translator=None):
    """Get existing config or create new one"""
    global _config_cache
    if _config_cache is None:
        _config_cache = setup_config(translator)
    return _config_cache