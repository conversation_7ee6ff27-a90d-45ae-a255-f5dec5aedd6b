#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证 Cursor 重置工具的功能
"""

import os
import sys
import json
import tempfile
import shutil
from colorama import Fore, Style, init

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入我们的模块
try:
    from config import get_config, setup_config
    from utils import get_user_documents_path, get_linux_cursor_path
    from reset_machine_manual import MachineIDResetter
    from logo import print_logo
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)

# 初始化 colorama
init()

class TestTranslator:
    """测试用的翻译器"""
    def get(self, key, **kwargs):
        return key.format(**kwargs) if kwargs else key

def test_logo():
    """测试 Logo 显示"""
    print(f"{Fore.CYAN}=== 测试 Logo 显示 ==={Style.RESET_ALL}")
    try:
        print_logo()
        print(f"{Fore.GREEN}✅ Logo 显示测试通过{Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ Logo 显示测试失败: {e}{Style.RESET_ALL}")
        return False

def test_config():
    """测试配置功能"""
    print(f"\n{Fore.CYAN}=== 测试配置功能 ==={Style.RESET_ALL}")
    try:
        translator = TestTranslator()
        config = get_config(translator)
        
        if config is None:
            print(f"{Fore.RED}❌ 配置获取失败{Style.RESET_ALL}")
            return False
        
        # 检查必要的配置节
        required_sections = []
        if sys.platform == "win32":
            required_sections = ['WindowsPaths', 'Language']
        elif sys.platform == "darwin":
            required_sections = ['MacPaths', 'Language']
        elif sys.platform == "linux":
            required_sections = ['LinuxPaths', 'Language']
        
        for section in required_sections:
            if not config.has_section(section):
                print(f"{Fore.RED}❌ 缺少配置节: {section}{Style.RESET_ALL}")
                return False
        
        print(f"{Fore.GREEN}✅ 配置功能测试通过{Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ 配置功能测试失败: {e}{Style.RESET_ALL}")
        return False

def test_utils():
    """测试工具函数"""
    print(f"\n{Fore.CYAN}=== 测试工具函数 ==={Style.RESET_ALL}")
    try:
        # 测试获取用户文档路径
        docs_path = get_user_documents_path()
        if not docs_path or not isinstance(docs_path, str):
            print(f"{Fore.RED}❌ 获取用户文档路径失败{Style.RESET_ALL}")
            return False
        
        print(f"{Fore.CYAN}📁 用户文档路径: {docs_path}{Style.RESET_ALL}")
        
        # 在 Linux 上测试 Cursor 路径检测
        if sys.platform == "linux":
            cursor_path = get_linux_cursor_path()
            print(f"{Fore.CYAN}📁 Linux Cursor 路径: {cursor_path}{Style.RESET_ALL}")
        
        print(f"{Fore.GREEN}✅ 工具函数测试通过{Style.RESET_ALL}")
        return True
    except Exception as e:
        print(f"{Fore.RED}❌ 工具函数测试失败: {e}{Style.RESET_ALL}")
        return False

def test_machine_id_generation():
    """测试机器 ID 生成"""
    print(f"\n{Fore.CYAN}=== 测试机器 ID 生成 ==={Style.RESET_ALL}")
    try:
        # 创建临时配置文件
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建临时配置
            config_dir = os.path.join(temp_dir, ".cursor-free-vip")
            os.makedirs(config_dir, exist_ok=True)
            
            config_file = os.path.join(config_dir, "config.ini")
            
            # 创建最小配置
            import configparser
            config = configparser.ConfigParser()
            
            if sys.platform == "win32":
                config.add_section('WindowsPaths')
                config.set('WindowsPaths', 'storage_path', os.path.join(temp_dir, "storage.json"))
                config.set('WindowsPaths', 'sqlite_path', os.path.join(temp_dir, "state.vscdb"))
            elif sys.platform == "darwin":
                config.add_section('MacPaths')
                config.set('MacPaths', 'storage_path', os.path.join(temp_dir, "storage.json"))
                config.set('MacPaths', 'sqlite_path', os.path.join(temp_dir, "state.vscdb"))
            elif sys.platform == "linux":
                config.add_section('LinuxPaths')
                config.set('LinuxPaths', 'storage_path', os.path.join(temp_dir, "storage.json"))
                config.set('LinuxPaths', 'sqlite_path', os.path.join(temp_dir, "state.vscdb"))
            
            with open(config_file, 'w') as f:
                config.write(f)
            
            # 创建虚拟的 storage.json
            storage_path = config.get(list(config.sections())[0], 'storage_path')
            os.makedirs(os.path.dirname(storage_path), exist_ok=True)
            
            initial_data = {
                "telemetry.devDeviceId": "old-device-id",
                "telemetry.machineId": "old-machine-id"
            }
            
            with open(storage_path, 'w') as f:
                json.dump(initial_data, f)
            
            # 临时修改配置路径
            original_platform = sys.platform
            
            # 创建 MachineIDResetter 实例（但不实际重置）
            translator = TestTranslator()
            
            # 只测试 ID 生成，不实际重置
            print(f"{Fore.CYAN}📝 测试 ID 生成功能...{Style.RESET_ALL}")
            
            # 手动生成新 ID 来测试
            import uuid
            import hashlib
            
            dev_device_id = str(uuid.uuid4())
            machine_id = hashlib.sha256(os.urandom(32)).hexdigest()
            mac_machine_id = hashlib.sha512(os.urandom(64)).hexdigest()
            sqm_id = "{" + str(uuid.uuid4()).upper() + "}"
            
            new_ids = {
                "telemetry.devDeviceId": dev_device_id,
                "telemetry.macMachineId": mac_machine_id,
                "telemetry.machineId": machine_id,
                "telemetry.sqmId": sqm_id,
                "storage.serviceMachineId": dev_device_id,
            }
            
            print(f"{Fore.CYAN}🆔 生成的新 ID:{Style.RESET_ALL}")
            for key, value in new_ids.items():
                print(f"  {key}: {value[:20]}...")
            
            # 验证 ID 格式
            if len(dev_device_id) != 36:  # UUID 长度
                print(f"{Fore.RED}❌ devDeviceId 格式错误{Style.RESET_ALL}")
                return False
            
            if len(machine_id) != 64:  # SHA256 长度
                print(f"{Fore.RED}❌ machineId 格式错误{Style.RESET_ALL}")
                return False
            
            if len(mac_machine_id) != 128:  # SHA512 长度
                print(f"{Fore.RED}❌ macMachineId 格式错误{Style.RESET_ALL}")
                return False
            
            print(f"{Fore.GREEN}✅ 机器 ID 生成测试通过{Style.RESET_ALL}")
            return True
            
    except Exception as e:
        print(f"{Fore.RED}❌ 机器 ID 生成测试失败: {e}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Cursor 重置工具测试套件{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    tests = [
        ("Logo 显示", test_logo),
        ("配置功能", test_config),
        ("工具函数", test_utils),
        ("机器 ID 生成", test_machine_id_generation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"{Fore.RED}❌ {test_name} 测试异常: {e}{Style.RESET_ALL}")
    
    print(f"\n{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}测试结果: {passed}/{total} 通过{Style.RESET_ALL}")
    
    if passed == total:
        print(f"{Fore.GREEN}🎉 所有测试通过！工具可以正常使用。{Style.RESET_ALL}")
        return True
    else:
        print(f"{Fore.YELLOW}⚠️  部分测试失败，请检查相关功能。{Style.RESET_ALL}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
