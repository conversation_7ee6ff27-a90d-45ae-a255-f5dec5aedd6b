#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版邮箱管理器 - 集成多种邮箱服务
"""

import random
import string
import requests
import json
import re
import time
from datetime import datetime

class EmailProviderBase:
    """邮箱服务提供者基类"""
    def get_name(self):
        raise NotImplementedError
    
    def generate_email(self):
        """返回(email, password, token, provider_extra)"""
        raise NotImplementedError
    
    def get_messages(self, email, token, provider_extra):
        raise NotImplementedError
    
    def get_message_detail(self, message_id, email, token, provider_extra):
        raise NotImplementedError

class MailTmProvider(EmailProviderBase):
    """Mail.tm 邮箱服务"""
    def get_name(self):
        return "mail.tm"
    
    def generate_email(self):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # 获取可用域名
        domains_response = requests.get("https://api.mail.tm/domains", headers=headers)
        domains_data = domains_response.json()
        if not isinstance(domains_data, list) or not domains_data:
            raise Exception("没有可用的域名")
        
        domain = domains_data[0]["domain"]
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@{domain}"
        password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
        
        # 注册账号
        register_data = {"address": email, "password": password}
        register_response = requests.post(
            "https://api.mail.tm/accounts",
            json=register_data,
            headers=headers
        )
        if not register_response.ok:
            raise Exception(f"注册账号失败: {register_response.status_code}")
        
        # 登录获取token
        login_data = {"address": email, "password": password}
        login_response = requests.post(
            "https://api.mail.tm/token",
            json=login_data,
            headers=headers
        )
        if not login_response.ok:
            raise Exception(f"登录失败: {login_response.status_code}")
        
        token_data = login_response.json()
        token = token_data.get("token")
        if not token:
            raise Exception("获取token失败")
        
        return email, password, token, {}
    
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        response = requests.get("https://api.mail.tm/messages", headers=headers)
        if response.ok:
            data = response.json()
            if isinstance(data, list):
                return data
            else:
                return data.get("hydra:member", [])
        return []
    
    def get_message_detail(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        
        response = requests.get(f"https://api.mail.tm/messages/{message_id}", headers=headers)
        if response.ok:
            return response.json()
        return None

class TempMailOrgProvider(EmailProviderBase):
    """TempMail.org 邮箱服务"""
    def get_name(self):
        return "tempmail.org"
    
    def generate_email(self):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        
        # 获取可用域名
        domains_response = requests.get("https://api.tempmail.org/request/domains/format/json/", headers=headers)
        if not domains_response.ok:
            raise Exception("无法获取域名列表")
        
        domains = domains_response.json()
        if not domains:
            raise Exception("没有可用的域名")
        
        domain = random.choice(domains)
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@{domain}"
        
        return email, None, None, {"username": username, "domain": domain}
    
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        
        url = f"https://api.tempmail.org/request/mail/id/{email}/format/json/"
        resp = requests.get(url, headers=headers)
        if resp.ok:
            return resp.json()
        return []
    
    def get_message_detail(self, message_id, email, token, provider_extra):
        return {"id": message_id, "subject": "邮件详情", "text": "请查看邮件列表"}

class MailsacProvider(EmailProviderBase):
    """Mailsac 邮箱服务"""
    def get_name(self):
        return "mailsac"
    
    def generate_email(self):
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@mailsac.com"
        return email, None, None, {"username": username}
    
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        
        try:
            resp = requests.get(f"https://mailsac.com/api/addresses/{email}/messages",
                              headers=headers, timeout=15)
            if resp.ok:
                messages = resp.json()
                return messages
            elif resp.status_code == 404:
                return []
        except Exception as e:
            print(f"Mailsac 获取邮件失败: {str(e)}")
        return []
    
    def get_message_detail(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        
        try:
            resp = requests.get(f"https://mailsac.com/api/addresses/{email}/messages/{message_id}",
                              headers=headers, timeout=15)
            if resp.ok:
                return resp.json()
        except Exception as e:
            print(f"Mailsac 获取邮件详情失败: {str(e)}")
        return None

class Original2925Provider(EmailProviderBase):
    """原始2925邮箱服务 - 集成现有的email_manager"""
    def get_name(self):
        return "2925.com (原版)"
    
    def generate_email(self):
        try:
            # 使用现有的email_manager
            import email_manager
            manager = email_manager.EmailManager()
            email = manager.generate_sub_email()
            
            # 返回格式化的结果
            return email, None, None, {"email": email, "manager": manager}
        except Exception as e:
            raise Exception(f"生成2925邮箱失败: {str(e)}")
    
    def get_messages(self, email, token, provider_extra):
        try:
            manager = provider_extra.get("manager")
            if manager:
                # 使用现有的验证码获取方法
                code = manager.get_verification_code(email)
                if code:
                    return [{"id": "1", "subject": "验证码", "text": f"验证码: {code}", "from": "system"}]
            return []
        except Exception as e:
            print(f"获取2925邮件失败: {str(e)}")
            return []
    
    def get_message_detail(self, message_id, email, token, provider_extra):
        return {"id": message_id, "subject": "验证码邮件", "text": "请查看邮件列表"}

class EnhancedEmailManager:
    """增强版邮箱管理器"""
    
    def __init__(self):
        # 初始化所有邮箱服务提供者
        self.providers = [
            Original2925Provider(),  # 优先使用原版2925服务
            MailTmProvider(),
            TempMailOrgProvider(),
            MailsacProvider()
        ]
        
        self.current_provider = self.providers[0]  # 默认使用2925服务
        self.current_email = None
        self.current_token = None
        self.current_provider_extra = None
        
        print(f"邮箱管理器初始化完成，可用服务: {[p.get_name() for p in self.providers]}")
    
    def get_available_providers(self):
        """获取可用的邮箱服务列表"""
        return [(i, provider.get_name()) for i, provider in enumerate(self.providers)]
    
    def set_provider(self, provider_index):
        """设置当前使用的邮箱服务"""
        if 0 <= provider_index < len(self.providers):
            self.current_provider = self.providers[provider_index]
            print(f"切换到邮箱服务: {self.current_provider.get_name()}")
            return True
        return False
    
    def generate_email(self):
        """生成临时邮箱"""
        try:
            email, password, token, provider_extra = self.current_provider.generate_email()
            
            self.current_email = email
            self.current_token = token
            self.current_provider_extra = provider_extra
            
            print(f"使用 {self.current_provider.get_name()} 生成邮箱: {email}")
            return email
        except Exception as e:
            print(f"生成邮箱失败: {str(e)}")
            raise e
    
    def get_verification_code(self, email=None, timeout=120):
        """获取验证码"""
        if not email:
            email = self.current_email
        
        if not email:
            raise Exception("没有可用的邮箱地址")
        
        print(f"开始监控邮箱 {email} 的验证码...")
        
        start_time = time.time()
        check_count = 0
        
        while time.time() - start_time < timeout:
            try:
                check_count += 1
                print(f"第 {check_count} 次检查邮件...")
                
                # 获取邮件列表
                messages = self.current_provider.get_messages(
                    email, self.current_token, self.current_provider_extra
                )
                
                if messages:
                    print(f"找到 {len(messages)} 封邮件")
                    
                    # 查找验证码
                    for message in messages:
                        code = self.extract_verification_code(message)
                        if code:
                            print(f"找到验证码: {code}")
                            return code
                
                # 等待3秒后继续检查
                time.sleep(3)
                
            except Exception as e:
                print(f"检查邮件时出错: {str(e)}")
                time.sleep(3)
        
        print("监控超时，未找到验证码")
        return None
    
    def extract_verification_code(self, message):
        """从邮件中提取验证码"""
        try:
            # 获取邮件内容
            content = ""
            if isinstance(message, dict):
                content += message.get("subject", "") + " "
                content += message.get("text", "") + " "
                content += message.get("body", "") + " "
                content += str(message.get("html", "")) + " "
            else:
                content = str(message)
            
            # 验证码正则表达式模式
            patterns = [
                r'验证码[：:\s]*([0-9]{4,8})',
                r'verification code[：:\s]*([0-9]{4,8})',
                r'code[：:\s]*([0-9]{4,8})',
                r'验证码为[：:\s]*([0-9]{4,8})',
                r'您的验证码是[：:\s]*([0-9]{4,8})',
                r'([0-9]{6})',  # 6位数字
                r'([0-9]{4})',  # 4位数字
                r'([0-9]{5})',  # 5位数字
                r'([0-9]{8})',  # 8位数字
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    code = matches[0]
                    # 验证码长度检查
                    if 4 <= len(code) <= 8:
                        return code
            
            return None
            
        except Exception as e:
            print(f"提取验证码时出错: {str(e)}")
            return None
    
    def get_messages(self, email=None):
        """获取邮件列表"""
        if not email:
            email = self.current_email
        
        if not email:
            return []
        
        try:
            return self.current_provider.get_messages(
                email, self.current_token, self.current_provider_extra
            )
        except Exception as e:
            print(f"获取邮件列表失败: {str(e)}")
            return []
    
    def test_provider(self, provider_index=None):
        """测试邮箱服务是否可用"""
        if provider_index is not None:
            if 0 <= provider_index < len(self.providers):
                provider = self.providers[provider_index]
            else:
                return False, "无效的服务索引"
        else:
            provider = self.current_provider
        
        try:
            print(f"测试邮箱服务: {provider.get_name()}")
            email, password, token, provider_extra = provider.generate_email()
            print(f"测试成功，生成邮箱: {email}")
            return True, f"服务正常，测试邮箱: {email}"
        except Exception as e:
            error_msg = f"服务异常: {str(e)}"
            print(error_msg)
            return False, error_msg

# 为了保持向后兼容性，创建一个别名
EmailManager = EnhancedEmailManager

def main():
    """测试函数"""
    print("=== 增强版邮箱管理器测试 ===")
    
    manager = EnhancedEmailManager()
    
    # 显示可用服务
    print("\n可用的邮箱服务:")
    for i, name in manager.get_available_providers():
        print(f"{i}. {name}")
    
    # 测试生成邮箱
    try:
        print("\n正在生成邮箱...")
        email = manager.generate_email()
        print(f"生成的邮箱: {email}")
        
        # 测试获取验证码（短时间测试）
        print("\n测试验证码监控（10秒）...")
        code = manager.get_verification_code(timeout=10)
        if code:
            print(f"找到验证码: {code}")
        else:
            print("未找到验证码（正常，因为没有实际发送）")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    main()
