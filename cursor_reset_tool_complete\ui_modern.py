#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化简洁美观的GUI界面设计
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFrame, 
                            QMessageBox, QProgressBar, QTextEdit, QScrollArea,
                            QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QPixmap, QIcon, QPalette, QColor, QPainter, QLinearGradient

class WorkerThread(QThread):
    """后台工作线程"""
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    
    def __init__(self, task_type):
        super().__init__()
        self.task_type = task_type
    
    def run(self):
        try:
            if self.task_type == "reset_machine":
                self.reset_machine_id()
            elif self.task_type == "email_manager":
                self.run_email_manager()
            elif self.task_type == "quit_cursor":
                self.quit_cursor_process()
            elif self.task_type == "delete_state":
                self.delete_state_db()
            elif self.task_type == "verify_order":
                self.verify_order()
        except Exception as e:
            self.finished.emit(False, str(e))
    
    def reset_machine_id(self):
        """重置机器ID"""
        self.message.emit("正在初始化...")
        self.progress.emit(20)
        
        try:
            import reset_machine_manual
            
            class SimpleTranslator:
                def get(self, key, **kwargs):
                    return key.format(**kwargs) if kwargs else key
            
            translator = SimpleTranslator()
            self.message.emit("正在生成新的机器ID...")
            self.progress.emit(60)
            
            result = reset_machine_manual.run(translator)
            self.progress.emit(100)
            
            if result:
                self.finished.emit(True, "机器ID重置成功！")
            else:
                self.finished.emit(False, "机器ID重置失败")
        except Exception as e:
            self.finished.emit(False, f"重置过程出错: {str(e)}")
    
    def run_email_manager(self):
        """运行邮箱管理器"""
        self.message.emit("正在启动邮箱管理器...")
        self.progress.emit(30)
        
        try:
            import email_manager
            manager = email_manager.EmailManager()
            
            self.message.emit("正在生成临时邮箱...")
            self.progress.emit(70)
            
            email = manager.generate_sub_email()
            self.progress.emit(100)
            self.finished.emit(True, f"临时邮箱已生成: {email}")
        except Exception as e:
            self.finished.emit(False, f"邮箱管理器错误: {str(e)}")
    
    def quit_cursor_process(self):
        """关闭Cursor进程"""
        self.message.emit("正在查找Cursor进程...")
        self.progress.emit(40)
        
        try:
            import quit_cursor
            
            class SimpleTranslator:
                def get(self, key, **kwargs):
                    return key.format(**kwargs) if kwargs else key
            
            translator = SimpleTranslator()
            self.message.emit("正在关闭进程...")
            self.progress.emit(80)
            
            quit_cursor.quit_cursor(translator)
            self.progress.emit(100)
            self.finished.emit(True, "Cursor进程已关闭")
        except Exception as e:
            self.finished.emit(False, f"关闭进程错误: {str(e)}")
    
    def delete_state_db(self):
        """删除状态数据库"""
        self.message.emit("正在查找状态数据库...")
        self.progress.emit(40)
        
        try:
            import delete_state_db
            self.message.emit("正在清理数据库...")
            self.progress.emit(80)
            
            delete_state_db.delete_state_db()
            self.progress.emit(100)
            self.finished.emit(True, "状态数据库已清理")
        except Exception as e:
            self.finished.emit(False, f"清理数据库错误: {str(e)}")
    
    def verify_order(self):
        """订单验证"""
        self.message.emit("正在启动订单验证...")
        self.progress.emit(30)

        try:
            from ui_order_modern import show_modern_order_dialog
            self.progress.emit(70)

            # 在主线程中显示对话框
            self.progress.emit(100)
            self.finished.emit(True, "订单验证窗口已打开")
        except Exception as e:
            self.finished.emit(False, f"订单验证错误: {str(e)}")

class ModernButton(QPushButton):
    """现代化按钮组件"""
    
    def __init__(self, text, icon_text="", color="#4A90E2"):
        super().__init__()
        self.setText(text)
        self.icon_text = icon_text
        self.color = color
        self.setup_style()
        self.setup_animation()
    
    def setup_style(self):
        """设置按钮样式"""
        self.setMinimumHeight(60)
        self.setMinimumWidth(200)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 3)
        self.setGraphicsEffect(shadow)
        
        self.setStyleSheet(f"""
            ModernButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.color}, stop:1 {self.darken_color(self.color)});
                border: none;
                border-radius: 12px;
                color: white;
                font-size: 14px;
                font-weight: 600;
                padding: 15px 25px;
                text-align: left;
            }}
            ModernButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.lighten_color(self.color)}, stop:1 {self.color});
                transform: translateY(-2px);
            }}
            ModernButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.darken_color(self.color)}, stop:1 {self.darken_color(self.color, 0.3)});
                transform: translateY(1px);
            }}
        """)
    
    def setup_animation(self):
        """设置动画效果"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def darken_color(self, color, factor=0.2):
        """加深颜色"""
        color_obj = QColor(color)
        h, s, v, a = color_obj.getHsv()
        v = max(0, int(v * (1 - factor)))
        color_obj.setHsv(h, s, v, a)
        return color_obj.name()
    
    def lighten_color(self, color, factor=0.2):
        """变浅颜色"""
        color_obj = QColor(color)
        h, s, v, a = color_obj.getHsv()
        v = min(255, int(v * (1 + factor)))
        color_obj.setHsv(h, s, v, a)
        return color_obj.name()
    
    def paintEvent(self, event):
        """自定义绘制事件"""
        super().paintEvent(event)
        
        if self.icon_text:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # 绘制图标
            icon_font = QFont("Segoe UI Emoji", 20)
            painter.setFont(icon_font)
            painter.setPen(QColor(255, 255, 255, 200))
            
            icon_rect = QRect(15, 0, 40, self.height())
            painter.drawText(icon_rect, Qt.AlignmentFlag.AlignCenter, self.icon_text)

class ModernMainWindow(QMainWindow):
    """现代化主窗口"""
    
    def __init__(self):
        super().__init__()
        self.worker_thread = None
        self.init_ui()
        self.apply_modern_style()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("Cursor Pro - 无限续杯工具")
        self.setFixedSize(480, 700)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 创建主容器
        main_container = QWidget()
        main_container.setObjectName("mainContainer")
        self.setCentralWidget(main_container)
        
        # 主布局
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(0)
        
        # 创建标题栏
        self.create_title_bar(main_layout)
        
        # 创建内容区域
        self.create_content_area(main_layout)
        
        # 创建状态栏
        self.create_status_area(main_layout)
        
        # 添加窗口阴影
        self.add_window_shadow()
    
    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_frame = QFrame()
        title_frame.setObjectName("titleFrame")
        title_frame.setFixedHeight(80)
        
        title_layout = QHBoxLayout(title_frame)
        title_layout.setContentsMargins(20, 15, 20, 15)
        
        # Logo和标题
        logo_label = QLabel("🎯")
        logo_label.setStyleSheet("font-size: 32px;")
        
        title_text = QVBoxLayout()
        title_label = QLabel("Cursor Pro")
        title_label.setObjectName("titleLabel")
        
        subtitle_label = QLabel("无限续杯工具 v2.2.3")
        subtitle_label.setObjectName("subtitleLabel")
        
        title_text.addWidget(title_label)
        title_text.addWidget(subtitle_label)
        title_text.setSpacing(2)
        
        # 关闭按钮
        close_btn = QPushButton("✕")
        close_btn.setObjectName("closeButton")
        close_btn.setFixedSize(30, 30)
        close_btn.clicked.connect(self.close)
        
        title_layout.addWidget(logo_label)
        title_layout.addLayout(title_text)
        title_layout.addStretch()
        title_layout.addWidget(close_btn)
        
        parent_layout.addWidget(title_frame)
        
        # 使标题栏可拖拽
        title_frame.mousePressEvent = self.mouse_press_event
        title_frame.mouseMoveEvent = self.mouse_move_event
    
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(25, 25, 25, 25)
        content_layout.setSpacing(20)
        
        # 功能按钮
        buttons_data = [
            ("🔄 重置机器ID", "🔄", "#4A90E2", "reset_machine"),
            ("📧 邮箱管理", "📧", "#50C878", "email_manager"),
            ("⚙️ 关闭进程", "⚙️", "#FF6B6B", "quit_cursor"),
            ("🧹 清理数据", "🧹", "#9B59B6", "delete_state"),
            ("💰 订单验证", "💰", "#F39C12", "verify_order")
        ]
        
        self.buttons = {}
        for text, icon, color, action in buttons_data:
            btn = ModernButton(text, icon, color)
            btn.clicked.connect(lambda checked, a=action: self.handle_button_click(a))
            content_layout.addWidget(btn)
            self.buttons[action] = btn
        
        # 添加弹性空间
        content_layout.addStretch()
        
        parent_layout.addWidget(content_frame)
    
    def create_status_area(self, parent_layout):
        """创建状态区域"""
        status_frame = QFrame()
        status_frame.setObjectName("statusFrame")
        status_frame.setFixedHeight(100)
        
        status_layout = QVBoxLayout(status_frame)
        status_layout.setContentsMargins(25, 15, 25, 15)
        status_layout.setSpacing(8)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("modernProgressBar")
        self.progress_bar.setVisible(False)
        self.progress_bar.setFixedHeight(6)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        status_layout.addWidget(self.progress_bar)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        parent_layout.addWidget(status_frame)
    
    def add_window_shadow(self):
        """添加窗口阴影"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 10)
        self.centralWidget().setGraphicsEffect(shadow)
    
    def apply_modern_style(self):
        """应用现代化样式"""
        self.setStyleSheet("""
            #mainContainer {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 20px;
            }
            
            #titleFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-radius: 20px 20px 0px 0px;
                border-bottom: 1px solid #e9ecef;
            }
            
            #titleLabel {
                color: #2c3e50;
                font-size: 20px;
                font-weight: 700;
                font-family: 'Segoe UI', sans-serif;
            }
            
            #subtitleLabel {
                color: #6c757d;
                font-size: 12px;
                font-weight: 400;
                font-family: 'Segoe UI', sans-serif;
            }
            
            #closeButton {
                background: #ff5f56;
                border: none;
                border-radius: 15px;
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
            
            #closeButton:hover {
                background: #ff3b30;
            }
            
            #contentFrame {
                background: transparent;
            }
            
            #statusFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #ffffff);
                border-radius: 0px 0px 20px 20px;
                border-top: 1px solid #e9ecef;
            }
            
            #modernProgressBar {
                border: none;
                border-radius: 3px;
                background: #e9ecef;
            }
            
            #modernProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4A90E2, stop:1 #50C878);
                border-radius: 3px;
            }
            
            #statusLabel {
                color: #495057;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Segoe UI', sans-serif;
            }
        """)
    
    def mouse_press_event(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouse_move_event(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def handle_button_click(self, action):
        """处理按钮点击"""
        # 特殊处理订单验证
        if action == "verify_order":
            self.handle_order_verification()
            return

        # 禁用所有按钮
        for btn in self.buttons.values():
            btn.setEnabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 启动工作线程
        self.worker_thread = WorkerThread(action)
        self.worker_thread.finished.connect(self.on_task_finished)
        self.worker_thread.progress.connect(self.progress_bar.setValue)
        self.worker_thread.message.connect(self.status_label.setText)
        self.worker_thread.start()

        # 更新状态
        action_names = {
            "reset_machine": "正在重置机器ID...",
            "email_manager": "正在启动邮箱管理...",
            "quit_cursor": "正在关闭Cursor进程...",
            "delete_state": "正在清理状态数据..."
        }
        self.status_label.setText(action_names.get(action, "正在处理..."))

    def handle_order_verification(self):
        """处理订单验证"""
        try:
            from ui_order_modern import show_modern_order_dialog
            self.status_label.setText("正在打开订单验证...")

            success = show_modern_order_dialog(self)
            if success:
                self.status_label.setText("✅ 订单验证成功")
                self.show_success_message("验证成功", "订单验证通过！")
            else:
                self.status_label.setText("订单验证已取消")

            # 3秒后重置状态
            QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))

        except Exception as e:
            self.status_label.setText(f"❌ 订单验证错误")
            self.show_error_message("验证失败", f"订单验证错误: {str(e)}")
    
    def on_task_finished(self, success, message):
        """任务完成回调"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 重新启用按钮
        for btn in self.buttons.values():
            btn.setEnabled(True)
        
        # 更新状态
        if success:
            self.status_label.setText(f"✅ {message}")
            self.show_success_message("操作成功", message)
        else:
            self.status_label.setText(f"❌ {message}")
            self.show_error_message("操作失败", message)
        
        # 3秒后重置状态
        QTimer.singleShot(3000, lambda: self.status_label.setText("就绪"))
    
    def show_success_message(self, title, message):
        """显示成功消息"""
        msg = QMessageBox(self)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Icon.Information)
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                color: #2c3e50;
            }
            QMessageBox QPushButton {
                background: #4A90E2;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 600;
            }
            QMessageBox QPushButton:hover {
                background: #357ABD;
            }
        """)
        msg.exec()
    
    def show_error_message(self, title, message):
        """显示错误消息"""
        msg = QMessageBox(self)
        msg.setWindowTitle(title)
        msg.setText(message)
        msg.setIcon(QMessageBox.Icon.Critical)
        msg.setStyleSheet("""
            QMessageBox {
                background: white;
                color: #2c3e50;
            }
            QMessageBox QPushButton {
                background: #FF6B6B;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 600;
            }
            QMessageBox QPushButton:hover {
                background: #FF5252;
            }
        """)
        msg.exec()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("Cursor Pro")
    app.setApplicationVersion("2.2.3")
    app.setOrganizationName("Pin Studios")
    
    # 设置全局字体
    font = QFont("Segoe UI", 9)
    app.setFont(font)
    
    # 创建并显示主窗口
    window = ModernMainWindow()
    
    # 居中显示
    screen = app.primaryScreen().geometry()
    window.move(
        (screen.width() - window.width()) // 2,
        (screen.height() - window.height()) // 2
    )
    
    window.show()
    
    print("🎨 现代化GUI界面启动成功！")
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
