import os
import sys
import time
import platform
from colorama import Fore, Style, init
from quit_cursor import quit_cursor

# 初始化colorama
init()

# 定义emoji常量
EMOJI = {
    "PROCESS": "⚙️",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "INFO": "ℹ️",
    "WAIT": "⏳"
}

class SimpleTranslator:
    """简单的翻译类，用于兼容quit_cursor模块"""
    def get(self, key, **kwargs):
        translations = {
            'quit_cursor.start': '正在关闭Cursor进程',
            'quit_cursor.no_process': '未发现运行中的Cursor进程',
            'quit_cursor.terminating': f'正在终止Cursor进程 (PID: {kwargs.get("pid", "未知")})',
            'quit_cursor.waiting': '等待Cursor进程关闭',
            'quit_cursor.success': '所有Cursor进程已成功关闭',
            'quit_cursor.timeout': f'无法在规定时间内关闭Cursor进程 (PIDs: {kwargs.get("pids", "未知")})',
            'quit_cursor.error': f'关闭Cursor进程时发生错误: {kwargs.get("error", "未知错误")}'
        }
        return translations.get(key, key)

def get_state_db_path():
    """根据操作系统获取Cursor状态文件路径"""
    system = platform.system()
    
    if system == "Darwin":  # macOS
        return os.path.abspath(os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/state.vscdb"))
    elif system == "Windows":  # Windows
        return os.path.abspath(os.path.expanduser("~/AppData/Roaming/Cursor/User/globalStorage/state.vscdb"))
    elif system == "Linux":  # Linux
        return os.path.abspath(os.path.expanduser("~/.config/Cursor/User/globalStorage/state.vscdb"))
    else:
        print(f"{Fore.RED}{EMOJI['ERROR']} 不支持的操作系统: {system}{Style.RESET_ALL}")
        return None

def delete_state_db():
    """删除Cursor的state.vscdb文件"""
    try:
        # 首先确保Cursor已关闭
        translator = SimpleTranslator()
        print(f"{Fore.CYAN}{EMOJI['PROCESS']} 准备删除Cursor状态文件，首先确保Cursor已关闭...{Style.RESET_ALL}")
        
        # 调用quit_cursor函数关闭Cursor
        if not quit_cursor(translator=translator, timeout=10):
            print(f"{Fore.YELLOW}{EMOJI['WAIT']} Cursor进程可能仍在运行，等待5秒后继续...{Style.RESET_ALL}")
            time.sleep(5)
        
        # 获取当前操作系统的state.vscdb文件路径
        state_db_path = get_state_db_path()
        if not state_db_path:
            return False
        
        # 检查文件是否存在
        if not os.path.exists(state_db_path):
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 状态文件不存在: {state_db_path}{Style.RESET_ALL}")
            return False
        
        # 删除文件
        print(f"{Fore.CYAN}{EMOJI['PROCESS']} 正在删除状态文件: {state_db_path}{Style.RESET_ALL}")
        os.remove(state_db_path)
        
        # 验证文件是否已删除
        if not os.path.exists(state_db_path):
            print(f"{Fore.GREEN}{EMOJI['SUCCESS']} 状态文件已成功删除{Style.RESET_ALL}")
            return True
        else:
            print(f"{Fore.RED}{EMOJI['ERROR']} 无法删除状态文件{Style.RESET_ALL}")
            return False
            
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 删除状态文件时发生错误: {str(e)}{Style.RESET_ALL}")
        return False

if __name__ == "__main__":
    system = platform.system()
    print(f"{Fore.CYAN}{EMOJI['INFO']} 当前操作系统: {system}{Style.RESET_ALL}")
    delete_state_db()
    print(f"{Fore.CYAN}{EMOJI['INFO']} 程序执行完毕，按任意键退出...{Style.RESET_ALL}")
    input() 