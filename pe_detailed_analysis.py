#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的PE文件分析工具
"""

import os
import sys
import struct
import re
from datetime import datetime

def extract_strings_from_section(data, min_length=4):
    """从数据中提取字符串"""
    # ASCII字符串
    ascii_strings = re.findall(rb'[\x20-\x7E]{' + str(min_length).encode() + rb',}', data)
    # Unicode字符串 (UTF-16LE)
    unicode_strings = re.findall(rb'(?:[\x20-\x7E]\x00){' + str(min_length).encode() + rb',}', data)
    
    result = []
    for s in ascii_strings:
        try:
            result.append(('ASCII', s.decode('ascii')))
        except:
            pass
    
    for s in unicode_strings:
        try:
            result.append(('Unicode', s.decode('utf-16le')))
        except:
            pass
    
    return result

def analyze_pe_detailed(filename):
    """详细分析PE文件"""
    try:
        with open(filename, 'rb') as f:
            print(f"文件: {filename}")
            print(f"大小: {os.path.getsize(filename):,} 字节")
            print("=" * 60)
            
            # DOS头
            dos_header = f.read(64)
            pe_offset = struct.unpack('<L', dos_header[60:64])[0]
            
            # PE头
            f.seek(pe_offset)
            pe_signature = f.read(4)
            
            # COFF头
            coff_header = f.read(20)
            machine = struct.unpack('<H', coff_header[0:2])[0]
            num_sections = struct.unpack('<H', coff_header[2:4])[0]
            timestamp = struct.unpack('<L', coff_header[4:8])[0]
            
            # 转换时间戳
            try:
                dt = datetime.fromtimestamp(timestamp)
                print(f"编译时间: {dt.strftime('%Y-%m-%d %H:%M:%S')}")
            except:
                print(f"时间戳: {timestamp} (无法解析)")
            
            # 可选头
            optional_header_size = struct.unpack('<H', coff_header[16:18])[0]
            optional_header = f.read(optional_header_size)
            
            # 节表
            print(f"\n节信息 (共{num_sections}个节):")
            print("-" * 60)
            sections = []
            
            for i in range(num_sections):
                section_header = f.read(40)
                if len(section_header) < 40:
                    break
                
                name = section_header[0:8].rstrip(b'\x00').decode('ascii', errors='ignore')
                virtual_size = struct.unpack('<L', section_header[8:12])[0]
                virtual_address = struct.unpack('<L', section_header[12:16])[0]
                raw_size = struct.unpack('<L', section_header[16:20])[0]
                raw_address = struct.unpack('<L', section_header[20:24])[0]
                characteristics = struct.unpack('<L', section_header[36:40])[0]
                
                sections.append({
                    'name': name,
                    'virtual_size': virtual_size,
                    'virtual_address': virtual_address,
                    'raw_size': raw_size,
                    'raw_address': raw_address,
                    'characteristics': characteristics
                })
                
                print(f"{name:8s} | VA: 0x{virtual_address:08X} | Size: {raw_size:8,} | Offset: 0x{raw_address:08X}")
            
            # 分析每个节的字符串
            print(f"\n字符串分析:")
            print("-" * 60)
            
            all_strings = []
            for section in sections:
                if section['raw_size'] > 0:
                    f.seek(section['raw_address'])
                    section_data = f.read(min(section['raw_size'], 1024*1024))  # 最多读1MB
                    
                    strings = extract_strings_from_section(section_data, 4)
                    
                    # 过滤有趣的字符串
                    interesting = []
                    keywords = ['http', 'www', 'exe', 'dll', 'version', 'copyright', 'microsoft', 
                               'windows', 'system', 'program', 'software', 'application', 'error',
                               'message', 'dialog', 'button', 'menu', 'file', 'path', 'registry']
                    
                    for str_type, string in strings:
                        if len(string) >= 4 and len(string) <= 200:
                            if any(keyword in string.lower() for keyword in keywords):
                                interesting.append((str_type, string))
                            elif any(c.isupper() for c in string) and any(c.islower() for c in string):
                                # 包含大小写混合的字符串通常更有意义
                                interesting.append((str_type, string))
                    
                    if interesting:
                        print(f"\n节 {section['name']} 中的有趣字符串:")
                        for str_type, string in interesting[:10]:  # 只显示前10个
                            print(f"  [{str_type:7s}] {string}")
                        
                        all_strings.extend(interesting)
            
            # 查找可能的框架/工具特征
            print(f"\n可能的开发工具/框架特征:")
            print("-" * 60)
            
            framework_indicators = {
                'Python': ['python', 'pyinstaller', 'pyz', '_pyi_', 'site-packages'],
                'Node.js': ['node.exe', 'electron', 'v8', 'nodejs'],
                'C#/.NET': ['mscorlib', 'System.', 'Microsoft.', '.NET', 'mscoree'],
                'C++': ['MSVCR', 'MSVCP', 'api-ms-win', 'kernel32', 'user32'],
                'Go': ['go build', 'runtime.', 'golang'],
                'Rust': ['rust', 'cargo'],
                'Java': ['java', 'jvm', 'class'],
                'Delphi': ['delphi', 'borland', 'vcl'],
                'AutoIt': ['autoit', 'au3'],
                'NSIS': ['nsis', 'nullsoft']
            }
            
            detected_frameworks = []
            all_text = ' '.join([s[1].lower() for s in all_strings])
            
            for framework, indicators in framework_indicators.items():
                if any(indicator in all_text for indicator in indicators):
                    detected_frameworks.append(framework)
            
            if detected_frameworks:
                print("可能使用的技术:")
                for fw in detected_frameworks:
                    print(f"  - {fw}")
            else:
                print("未检测到明显的框架特征")
            
            # 总结
            print(f"\n分析总结:")
            print("-" * 60)
            print(f"• 文件类型: 64位 Windows PE 可执行文件")
            print(f"• 文件大小: {os.path.getsize(filename):,} 字节")
            print(f"• 节数量: {num_sections}")
            print(f"• 提取字符串: {len(all_strings)} 个有意义的字符串")
            
            if detected_frameworks:
                print(f"• 可能的开发技术: {', '.join(detected_frameworks)}")
            
    except Exception as e:
        print(f"分析出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_pe_detailed("无线续杯Win系统v2.2.3版本.exe")
