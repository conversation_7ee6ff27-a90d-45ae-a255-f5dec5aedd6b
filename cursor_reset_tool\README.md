# Cursor Pro 无限续杯工具 v2.2.3

这是一个用于重置 Cursor 编辑器机器 ID 的工具，可以绕过试用限制，实现"无限续杯"功能。

## 功能特性

- ✅ **重置 Cursor 机器 ID** - 绕过试用限制
- ✅ **跨平台支持** - Windows、macOS、Linux
- ✅ **自动备份** - 修改前自动创建备份文件
- ✅ **智能路径检测** - 自动检测 Cursor 安装路径
- ✅ **多语言支持** - 中文、英文等
- ✅ **管理员权限检测** - Windows 下自动请求管理员权限

## 系统要求

- Python 3.7+
- Windows 10/11, macOS 10.14+, 或 Linux
- Cursor 编辑器 0.45.0+

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 方法一：直接运行
```bash
python main.py
```

### 方法二：打包成可执行文件
```bash
# 安装 PyInstaller
pip install pyinstaller

# 打包成单个可执行文件
pyinstaller --onefile --noconsole --name "无线续杯Win系统v2.2.3版本" main.py
```

## 工作原理

1. **机器 ID 重置**：生成新的设备 ID、机器 ID 等标识符
2. **配置文件更新**：更新 Cursor 的配置文件和数据库
3. **系统级 ID 更新**：在 Windows 上更新注册表中的机器 GUID
4. **文件备份**：所有修改前都会创建备份文件

## 文件结构

```
cursor_reset_tool/
├── main.py                    # 主程序入口
├── config.py                  # 配置管理
├── reset_machine_manual.py    # 核心重置功能
├── utils.py                   # 工具函数
├── logo.py                    # Logo 显示
├── requirements.txt           # 依赖列表
└── README.md                  # 说明文档
```

## 注意事项

⚠️ **重要提醒**：
- 使用前请关闭 Cursor 编辑器
- Windows 系统需要管理员权限
- 建议在使用前备份重要数据
- 本工具仅供学习研究使用

## 故障排除

### 常见问题

1. **权限不足**
   - Windows：以管理员身份运行
   - macOS/Linux：使用 `sudo` 运行

2. **找不到 Cursor 路径**
   - 检查 Cursor 是否正确安装
   - 手动设置 `CURSOR_PATH` 环境变量

3. **配置文件损坏**
   - 删除 `~/.cursor-free-vip` 目录重新生成配置

## 技术细节

### 重置的标识符
- `telemetry.devDeviceId` - 设备 ID
- `telemetry.machineId` - 机器 ID  
- `telemetry.macMachineId` - Mac 机器 ID
- `telemetry.sqmId` - SQM ID
- `storage.serviceMachineId` - 服务机器 ID

### 修改的文件
- `storage.json` - Cursor 配置文件
- `state.vscdb` - SQLite 数据库
- `machineId` - 机器 ID 文件
- Windows 注册表 - 系统级机器 GUID

## 免责声明

本工具仅供学习和研究目的使用。使用者应当遵守相关法律法规和软件许可协议。作者不对使用本工具造成的任何后果承担责任。

## 贡献者

- Pin Studios (yeongpin)
- BasaiCorp
- aliensb
- handwerk2016
- 以及其他贡献者

## 许可证

本项目基于 MIT 许可证开源。

---

**GitHub**: https://github.com/yeongpin/cursor-free-vip
