#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全仿照原始软件的GUI界面
"""

import sys
import os
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QPushButton, QFrame,
                            QMessageBox, QProgressBar, QLineEdit, QDialog,
                            QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy,
                            QComboBox)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QPixmap, QIcon, QPalette, QColor, QPainter, QLinearGradient

class WorkerThread(QThread):
    """后台工作线程"""
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    
    def __init__(self, task_type):
        super().__init__()
        self.task_type = task_type
    
    def run(self):
        try:
            if self.task_type == "reset_machine":
                self.reset_machine_id()
            elif self.task_type == "email_manager":
                self.run_email_manager()
            elif self.task_type == "quit_cursor":
                self.quit_cursor_process()
            elif self.task_type == "delete_state":
                self.delete_state_db()
        except Exception as e:
            self.finished.emit(False, str(e))
    
    def reset_machine_id(self):
        """重置机器ID"""
        self.message.emit("正在重置机器ID...")
        self.progress.emit(30)
        
        try:
            import reset_machine_manual
            
            class SimpleTranslator:
                def get(self, key, **kwargs):
                    return key.format(**kwargs) if kwargs else key
            
            translator = SimpleTranslator()
            self.progress.emit(70)
            
            result = reset_machine_manual.run(translator)
            self.progress.emit(100)
            
            if result:
                self.finished.emit(True, "机器ID重置成功！")
            else:
                self.finished.emit(False, "机器ID重置失败")
        except Exception as e:
            self.finished.emit(False, f"重置过程出错: {str(e)}")
    
    def run_email_manager(self):
        """运行邮箱管理器"""
        self.message.emit("正在启动邮箱管理器...")
        self.progress.emit(40)
        
        try:
            import email_manager
            manager = email_manager.EmailManager()
            self.progress.emit(80)
            
            email = manager.generate_sub_email()
            self.progress.emit(100)
            self.finished.emit(True, f"临时邮箱已生成: {email}")
        except Exception as e:
            self.finished.emit(False, f"邮箱管理器错误: {str(e)}")
    
    def quit_cursor_process(self):
        """关闭Cursor进程"""
        self.message.emit("正在关闭Cursor进程...")
        self.progress.emit(50)
        
        try:
            import quit_cursor
            
            class SimpleTranslator:
                def get(self, key, **kwargs):
                    return key.format(**kwargs) if kwargs else key
            
            translator = SimpleTranslator()
            self.progress.emit(90)
            
            quit_cursor.quit_cursor(translator)
            self.progress.emit(100)
            self.finished.emit(True, "Cursor进程已关闭")
        except Exception as e:
            self.finished.emit(False, f"关闭进程错误: {str(e)}")
    
    def delete_state_db(self):
        """删除状态数据库"""
        self.message.emit("正在清理状态数据库...")
        self.progress.emit(50)
        
        try:
            import delete_state_db
            self.progress.emit(90)
            
            delete_state_db.delete_state_db()
            self.progress.emit(100)
            self.finished.emit(True, "状态数据库已清理")
        except Exception as e:
            self.finished.emit(False, f"清理数据库错误: {str(e)}")

class OriginalButton(QPushButton):
    """原始风格按钮"""
    
    def __init__(self, text, color_type="orange"):
        super().__init__(text)
        self.color_type = color_type
        self.setup_style()
    
    def setup_style(self):
        """设置按钮样式"""
        self.setMinimumHeight(50)
        self.setMinimumWidth(300)
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
        # 根据颜色类型设置不同的样式
        if self.color_type == "orange":
            border_color = "#ff6b35"
            hover_bg = "rgba(255, 107, 53, 0.1)"
        elif self.color_type == "cyan":
            border_color = "#00d4aa"
            hover_bg = "rgba(0, 212, 170, 0.1)"
        elif self.color_type == "purple":
            border_color = "#8b5cf6"
            hover_bg = "rgba(139, 92, 246, 0.1)"
        else:
            border_color = "#6b7280"
            hover_bg = "rgba(107, 114, 128, 0.1)"
        
        self.setStyleSheet(f"""
            OriginalButton {{
                background: transparent;
                border: 2px solid {border_color};
                border-radius: 8px;
                color: white;
                font-size: 16px;
                font-weight: 500;
                padding: 12px 20px;
                font-family: 'Microsoft YaHei', sans-serif;
            }}
            OriginalButton:hover {{
                background: {hover_bg};
                border-color: {border_color};
            }}
            OriginalButton:pressed {{
                background: rgba(255, 255, 255, 0.05);
            }}
        """)

class EmailManagerDialog(QDialog):
    """邮箱管理器对话框 - 完全仿照原始设计"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.apply_style()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("邮箱管理器")
        self.setFixedSize(410, 390)
        self.setModal(True)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("emailContainer")
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)
        
        # 内容布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 标题栏
        title_bar = QFrame()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(35)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 0, 15, 0)
        
        title_icon = QLabel("📧")
        title_icon.setStyleSheet("font-size: 16px; color: white;")
        title_label = QLabel("邮箱管理器")
        title_label.setObjectName("titleText")
        
        close_btn = QPushButton("✕")
        close_btn.setObjectName("closeButton")
        close_btn.setFixedSize(20, 20)
        close_btn.clicked.connect(self.close)
        
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(close_btn)
        
        # 内容区域
        content_area = QFrame()
        content_area.setObjectName("contentArea")
        
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(25, 25, 25, 25)
        content_layout.setSpacing(20)
        
        # 邮箱验证工具标题
        tool_title = QLabel("邮箱验证工具")
        tool_title.setObjectName("toolTitle")
        tool_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 紫色分割线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setObjectName("purpleLine")
        line.setFixedHeight(2)
        
        # 邮箱输入框
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("正在生成邮箱...")
        self.email_input.setObjectName("emailInput")
        self.email_input.setReadOnly(True)
        
        # 邮箱服务选择
        service_layout = QHBoxLayout()
        service_layout.setSpacing(10)

        service_label = QLabel("邮箱服务:")
        service_label.setObjectName("serviceLabel")

        self.service_combo = QComboBox()
        self.service_combo.setObjectName("serviceCombo")
        self.service_combo.currentIndexChanged.connect(self.on_service_changed)

        service_layout.addWidget(service_label)
        service_layout.addWidget(self.service_combo)
        service_layout.addStretch()

        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        domain_btn = OriginalButton("2925邮箱", "orange")
        domain_btn.clicked.connect(self.generate_2925_email)

        temp_btn = OriginalButton("临时邮箱", "purple")
        temp_btn.clicked.connect(self.generate_temp_email)

        button_layout.addWidget(domain_btn)
        button_layout.addWidget(temp_btn)
        
        # 获取验证码按钮
        verify_btn = OriginalButton("获取验证码", "cyan")
        verify_btn.clicked.connect(self.get_verification_code)
        
        content_layout.addWidget(tool_title)
        content_layout.addWidget(line)
        content_layout.addWidget(self.email_input)
        content_layout.addLayout(service_layout)
        content_layout.addLayout(button_layout)
        content_layout.addWidget(verify_btn)
        
        layout.addWidget(title_bar)
        layout.addWidget(content_area)

        # 初始化邮箱管理器
        self.init_email_manager()

        # 自动生成邮箱
        self.generate_2925_email()
    
    def init_email_manager(self):
        """初始化邮箱管理器"""
        try:
            from enhanced_email_manager import EnhancedEmailManager
            self.email_manager = EnhancedEmailManager()

            # 填充服务选择下拉框
            providers = self.email_manager.get_available_providers()
            for i, name in providers:
                self.service_combo.addItem(name, i)

            print("邮箱管理器初始化成功")
        except Exception as e:
            print(f"邮箱管理器初始化失败: {str(e)}")
            self.email_manager = None

    def on_service_changed(self, index):
        """邮箱服务切换"""
        if self.email_manager:
            provider_index = self.service_combo.itemData(index)
            if provider_index is not None:
                self.email_manager.set_provider(provider_index)
                print(f"切换到邮箱服务: {self.service_combo.currentText()}")

    def apply_style(self):
        """应用样式"""
        self.setStyleSheet("""
            #emailContainer {
                background: #1a1a1a;
                border: 1px solid #333;
                border-radius: 0px;
            }
            
            #titleBar {
                background: #2a2a2a;
                border-bottom: 1px solid #333;
            }
            
            #titleText {
                color: white;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #closeButton {
                background: #ff5f56;
                border: none;
                border-radius: 10px;
                color: white;
                font-size: 10px;
                font-weight: bold;
            }
            
            #closeButton:hover {
                background: #ff3b30;
            }
            
            #contentArea {
                background: #1a1a1a;
            }
            
            #toolTitle {
                color: white;
                font-size: 18px;
                font-weight: 600;
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 10px 0;
            }
            
            #purpleLine {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 #8b5cf6, stop:1 transparent);
                border: none;
                margin: 5px 0;
            }
            
            #emailInput {
                background: #2a2a2a;
                border: 1px solid #444;
                border-radius: 6px;
                color: #00d4aa;
                font-size: 14px;
                font-family: 'Consolas', monospace;
                padding: 12px 15px;
                min-height: 20px;
            }
            
            #emailInput:focus {
                border-color: #00d4aa;
            }

            #serviceLabel {
                color: white;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
            }

            #serviceCombo {
                background: #2a2a2a;
                border: 1px solid #444;
                border-radius: 6px;
                color: white;
                font-size: 13px;
                font-family: 'Microsoft YaHei', sans-serif;
                padding: 8px 12px;
                min-width: 120px;
            }

            #serviceCombo:hover {
                border-color: #8b5cf6;
            }

            #serviceCombo::drop-down {
                border: none;
                background: #444;
            }

            #serviceCombo::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid white;
                width: 0px;
                height: 0px;
            }

            #serviceCombo QAbstractItemView {
                background: #2a2a2a;
                border: 1px solid #444;
                color: white;
                selection-background-color: #8b5cf6;
            }
        """)
    
    def generate_2925_email(self):
        """生成2925邮箱"""
        try:
            if self.email_manager:
                # 切换到2925服务（索引0）
                self.email_manager.set_provider(0)
                self.service_combo.setCurrentIndex(0)

                email = self.email_manager.generate_email()
                self.email_input.setText(email)
                print(f"生成2925邮箱成功: {email}")
            else:
                # 回退到原始方法
                import email_manager
                manager = email_manager.EmailManager()
                email = manager.generate_sub_email()
                self.email_input.setText(email)
        except Exception as e:
            error_msg = f"生成失败: {str(e)}"
            self.email_input.setText(error_msg)
            print(error_msg)

    def generate_temp_email(self):
        """生成临时邮箱"""
        try:
            if self.email_manager:
                # 使用当前选择的服务
                email = self.email_manager.generate_email()
                self.email_input.setText(email)
                print(f"生成临时邮箱成功: {email}")
            else:
                # 回退方法
                self.generate_2925_email()
        except Exception as e:
            error_msg = f"生成失败: {str(e)}"
            self.email_input.setText(error_msg)
            print(error_msg)
    
    def get_verification_code(self):
        """获取验证码"""
        email = self.email_input.text()
        if email and "@" in email:
            # 启动验证码监控
            self.start_verification_monitoring(email)
        else:
            QMessageBox.warning(self, "错误", "请先生成有效的邮箱地址")

    def start_verification_monitoring(self, email):
        """启动验证码监控"""
        try:
            if self.email_manager:
                # 使用增强版邮箱管理器进行监控
                self.show_enhanced_monitor_dialog(email)
            else:
                # 回退到原始监控方法
                from email_monitor_dialog import show_email_monitor_dialog

                # 关闭当前对话框
                self.hide()

                # 显示监控对话框
                show_email_monitor_dialog(email, self.parent())

                # 重新显示当前对话框
                self.show()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动验证码监控失败: {str(e)}")

    def show_enhanced_monitor_dialog(self, email):
        """显示增强版监控对话框"""
        try:
            # 创建简化的监控对话框
            msg = QMessageBox(self)
            msg.setWindowTitle("验证码监控")
            msg.setText(f"正在监控邮箱: {email}\n\n请在Cursor中触发验证码发送...")
            msg.setStandardButtons(QMessageBox.StandardButton.Ok | QMessageBox.StandardButton.Cancel)
            msg.setDefaultButton(QMessageBox.StandardButton.Ok)

            # 设置样式
            msg.setStyleSheet("""
                QMessageBox {
                    background: #1a1a1a;
                    color: white;
                    font-family: 'Microsoft YaHei', sans-serif;
                }
                QMessageBox QPushButton {
                    background: transparent;
                    border: 2px solid #8b5cf6;
                    border-radius: 6px;
                    color: white;
                    padding: 8px 16px;
                    font-family: 'Microsoft YaHei', sans-serif;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background: rgba(139, 92, 246, 0.1);
                }
            """)

            result = msg.exec()
            if result == QMessageBox.StandardButton.Ok:
                # 启动验证码获取
                self.get_verification_code_async(email)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"监控对话框错误: {str(e)}")

    def get_verification_code_async(self, email):
        """异步获取验证码"""
        try:
            # 显示进度对话框
            progress_msg = QMessageBox(self)
            progress_msg.setWindowTitle("获取验证码")
            progress_msg.setText("正在获取验证码，请稍候...")
            progress_msg.setStandardButtons(QMessageBox.StandardButton.Cancel)
            progress_msg.setStyleSheet("""
                QMessageBox {
                    background: #1a1a1a;
                    color: white;
                    font-family: 'Microsoft YaHei', sans-serif;
                }
                QMessageBox QPushButton {
                    background: transparent;
                    border: 2px solid #ff6b6b;
                    border-radius: 6px;
                    color: white;
                    padding: 8px 16px;
                    font-family: 'Microsoft YaHei', sans-serif;
                }
                QMessageBox QPushButton:hover {
                    background: rgba(255, 107, 107, 0.1);
                }
            """)

            # 启动后台线程获取验证码
            import threading

            def get_code():
                try:
                    code = self.email_manager.get_verification_code(email, timeout=60)
                    if code:
                        # 在主线程中显示结果
                        self.show_verification_result(code, True)
                    else:
                        self.show_verification_result("未找到验证码", False)
                except Exception as e:
                    self.show_verification_result(f"获取失败: {str(e)}", False)
                finally:
                    # 关闭进度对话框
                    progress_msg.close()

            thread = threading.Thread(target=get_code, daemon=True)
            thread.start()

            # 显示进度对话框
            progress_msg.exec()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"获取验证码失败: {str(e)}")

    def show_verification_result(self, result, success):
        """显示验证码结果"""
        try:
            if success:
                # 成功获取验证码
                msg = QMessageBox(self)
                msg.setWindowTitle("验证码获取成功")
                msg.setText(f"验证码: {result}")
                msg.setStandardButtons(QMessageBox.StandardButton.Ok)

                # 复制到剪贴板
                clipboard = QApplication.clipboard()
                clipboard.setText(result)

                msg.setInformativeText("验证码已自动复制到剪贴板")
                msg.setStyleSheet("""
                    QMessageBox {
                        background: #1a1a1a;
                        color: white;
                        font-family: 'Microsoft YaHei', sans-serif;
                    }
                    QMessageBox QPushButton {
                        background: transparent;
                        border: 2px solid #00d4aa;
                        border-radius: 6px;
                        color: white;
                        padding: 8px 16px;
                        font-family: 'Microsoft YaHei', sans-serif;
                    }
                    QMessageBox QPushButton:hover {
                        background: rgba(0, 212, 170, 0.1);
                    }
                """)
                msg.exec()
            else:
                # 获取失败
                QMessageBox.warning(self, "获取失败", result)

        except Exception as e:
            print(f"显示结果时出错: {str(e)}")

class OriginalMainWindow(QMainWindow):
    """完全仿照原始软件的主窗口"""
    
    def __init__(self):
        super().__init__()
        self.worker_thread = None
        self.init_ui()
        self.apply_original_style()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("Cursor Free VIP")
        self.setFixedSize(343, 460)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 创建主容器
        main_container = QWidget()
        main_container.setObjectName("mainContainer")
        self.setCentralWidget(main_container)
        
        # 主布局
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建标题栏
        self.create_title_bar(main_layout)
        
        # 创建内容区域
        self.create_content_area(main_layout)
        
        # 添加窗口阴影
        self.add_window_shadow()
    
    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_bar = QFrame()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(35)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 0, 15, 0)
        
        # Logo和版本
        logo_label = QLabel("🎯")
        logo_label.setStyleSheet("font-size: 16px; color: white;")
        
        version_label = QLabel("v2.2.3")
        version_label.setObjectName("versionLabel")
        
        # 窗口控制按钮
        control_layout = QHBoxLayout()
        control_layout.setSpacing(5)
        
        minimize_btn = QPushButton("—")
        minimize_btn.setObjectName("controlBtn")
        minimize_btn.setFixedSize(20, 20)
        minimize_btn.clicked.connect(self.showMinimized)
        
        maximize_btn = QPushButton("□")
        maximize_btn.setObjectName("controlBtn")
        maximize_btn.setFixedSize(20, 20)
        
        close_btn = QPushButton("✕")
        close_btn.setObjectName("closeBtn")
        close_btn.setFixedSize(20, 20)
        close_btn.clicked.connect(self.close)
        
        control_layout.addWidget(minimize_btn)
        control_layout.addWidget(maximize_btn)
        control_layout.addWidget(close_btn)
        
        title_layout.addWidget(logo_label)
        title_layout.addWidget(version_label)
        title_layout.addStretch()
        title_layout.addLayout(control_layout)
        
        parent_layout.addWidget(title_bar)
        
        # 使标题栏可拖拽
        title_bar.mousePressEvent = self.mouse_press_event
        title_bar.mouseMoveEvent = self.mouse_move_event
    
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        content_area = QFrame()
        content_area.setObjectName("contentArea")
        
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(25, 25, 25, 25)
        content_layout.setSpacing(20)
        
        # 主标题
        main_title = QLabel("Cursor Free VIP")
        main_title.setObjectName("mainTitle")
        main_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 紫色分割线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setObjectName("gradientLine")
        line.setFixedHeight(2)
        
        # 副标题
        subtitle = QLabel("机器ID已重置")
        subtitle.setObjectName("subtitle")
        subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 另一条紫色分割线
        line2 = QFrame()
        line2.setFrameShape(QFrame.Shape.HLine)
        line2.setObjectName("gradientLine")
        line2.setFixedHeight(2)
        
        # 功能按钮
        self.unlimited_btn = OriginalButton("无限续杯", "orange")
        self.unlimited_btn.clicked.connect(self.handle_unlimited)

        self.reset_btn = OriginalButton("重置Cursor", "cyan")
        self.reset_btn.clicked.connect(self.handle_reset_cursor)

        # 邮箱管理按钮
        self.email_btn = OriginalButton("邮箱管理", "purple")
        self.email_btn.clicked.connect(self.handle_email_manager)

        content_layout.addWidget(main_title)
        content_layout.addWidget(line)
        content_layout.addWidget(subtitle)
        content_layout.addWidget(line2)
        content_layout.addWidget(self.unlimited_btn)
        content_layout.addWidget(self.reset_btn)
        content_layout.addWidget(self.email_btn)
        content_layout.addStretch()
        
        parent_layout.addWidget(content_area)
    
    def add_window_shadow(self):
        """添加窗口阴影"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 160))
        shadow.setOffset(0, 5)
        self.centralWidget().setGraphicsEffect(shadow)
    
    def apply_original_style(self):
        """应用原始样式"""
        self.setStyleSheet("""
            #mainContainer {
                background: #1a1a1a;
                border: 1px solid #333;
                border-radius: 0px;
            }
            
            #titleBar {
                background: #2a2a2a;
                border-bottom: 1px solid #333;
            }
            
            #versionLabel {
                color: #888;
                font-size: 12px;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #controlBtn {
                background: #444;
                border: none;
                border-radius: 3px;
                color: #ccc;
                font-size: 10px;
            }
            
            #controlBtn:hover {
                background: #555;
            }
            
            #closeBtn {
                background: #ff5f56;
                border: none;
                border-radius: 3px;
                color: white;
                font-size: 10px;
            }
            
            #closeBtn:hover {
                background: #ff3b30;
            }
            
            #contentArea {
                background: #1a1a1a;
            }
            
            #mainTitle {
                color: #8b5cf6;
                font-size: 20px;
                font-weight: 600;
                font-family: 'Microsoft YaHei', sans-serif;
                border: 2px solid #8b5cf6;
                border-radius: 8px;
                padding: 10px;
                margin: 10px 0;
            }
            
            #subtitle {
                color: white;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 10px 0;
            }
            
            #gradientLine {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 #8b5cf6, stop:1 transparent);
                border: none;
                margin: 5px 0;
            }
        """)
    
    def mouse_press_event(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
    
    def mouse_move_event(self, event):
        """鼠标移动事件"""
        if event.buttons() == Qt.MouseButton.LeftButton:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
    
    def handle_unlimited(self):
        """处理无限续杯"""
        reply = QMessageBox.question(self, "确认", "确定要执行无限续杯操作吗？", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.run_task("reset_machine", "正在执行无限续杯...")
    
    def handle_reset_cursor(self):
        """处理重置Cursor"""
        reply = QMessageBox.question(self, "确认", "确定要重置Cursor吗？",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.run_task("quit_cursor", "正在重置Cursor...")

    def handle_email_manager(self):
        """处理邮箱管理"""
        dialog = EmailManagerDialog(self)
        dialog.exec()
    
    def run_task(self, task_type, message):
        """运行任务"""
        # 禁用按钮
        self.unlimited_btn.setEnabled(False)
        self.reset_btn.setEnabled(False)
        self.email_btn.setEnabled(False)

        # 启动工作线程
        self.worker_thread = WorkerThread(task_type)
        self.worker_thread.finished.connect(self.on_task_finished)
        self.worker_thread.start()

    def on_task_finished(self, success, message):
        """任务完成回调"""
        # 重新启用按钮
        self.unlimited_btn.setEnabled(True)
        self.reset_btn.setEnabled(True)
        self.email_btn.setEnabled(True)

        # 显示结果
        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "错误", message)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("Cursor Free VIP")
    app.setApplicationVersion("2.2.3")
    
    # 设置全局字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 创建并显示主窗口
    window = OriginalMainWindow()
    
    # 居中显示
    screen = app.primaryScreen().geometry()
    window.move(
        (screen.width() - window.width()) // 2,
        (screen.height() - window.height()) // 2
    )
    
    window.show()
    
    print("🎨 原始风格GUI界面启动成功！")
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
