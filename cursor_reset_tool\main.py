# main.py
# This script allows the user to choose which script to run.
import os
import sys
import platform
from colorama import Fore, Style, init
import locale
from config import get_config, force_update_config
from utils import get_user_documents_path  

# Initialize colorama
init()

# Define emoji and color constants
EMOJI = {
    "FILE": "📄",
    "BACKUP": "💾",
    "SUCCESS": "✅",
    "ERROR": "❌",
    "INFO": "ℹ️",
    "RESET": "🔄",
    "MENU": "📋",
    "ARROW": "➜",
    "LANG": "🌐",
    "ADMIN": "🔐"
}

# Function to check if running as frozen executable
def is_frozen():
    """Check if the script is running as a frozen executable."""
    return getattr(sys, 'frozen', False)

# Function to check admin privileges (Windows only)
def is_admin():
    """Check if the script is running with admin privileges (Windows only)."""
    if platform.system() == 'Windows':
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False
    # Always return True for non-Windows to avoid changing behavior
    return True

# Function to restart with admin privileges
def run_as_admin():
    """Restart the current script with admin privileges (Windows only)."""
    if platform.system() != 'Windows':
        return False
        
    try:
        import ctypes
        args = [sys.executable] + sys.argv
        
        # Request elevation via ShellExecute
        print(f"{Fore.YELLOW}{EMOJI['ADMIN']} 请求管理员权限...{Style.RESET_ALL}")
        ctypes.windll.shell32.ShellExecuteW(None, "runas", args[0], " ".join('"' + arg + '"' for arg in args[1:]), None, 1)
        return True
    except Exception as e:
        print(f"{Fore.RED}{EMOJI['ERROR']} 无法以管理员权限重启: {e}{Style.RESET_ALL}")
        return False

class Translator:
    def __init__(self):
        self.translations = {}
        self.current_language = 'zh_cn'  # 默认中文
        self.fallback_language = 'en'
    
    def detect_system_language(self):
        """Detect system language and return corresponding language code"""
        try:
            # 简化的语言检测
            import locale
            locale.setlocale(locale.LC_ALL, '')
            system_locale = locale.getlocale()[0]
            if system_locale and 'chinese' in system_locale.lower():
                return 'zh_cn'
            return 'en'
        except:
            return 'zh_cn'  # 默认中文

    def get(self, key, **kwargs):
        """Get translated text with fallback support"""
        try:
            # For simplicity, just return the key since we're removing most functionality
            return key.format(**kwargs) if kwargs else key
        except Exception:
            return key

# Create translator instance
translator = Translator()

def print_menu():
    """Print menu options"""
    print(f"\n{Fore.CYAN}{EMOJI['MENU']} 菜单:{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}{'─' * 70}{Style.RESET_ALL}")
    
    print(f"{Fore.GREEN}1{Style.RESET_ALL}. {EMOJI['RESET']} 重置 Cursor 机器 ID")
    print(f"{Fore.GREEN}0{Style.RESET_ALL}. {EMOJI['ERROR']} 退出")
    
    print(f"{Fore.YELLOW}{'─' * 70}{Style.RESET_ALL}")

def main():
    # Check for admin privileges if running as executable on Windows only
    if platform.system() == 'Windows' and is_frozen() and not is_admin():
        print(f"{Fore.YELLOW}{EMOJI['ADMIN']} 需要管理员权限{Style.RESET_ALL}")
        if run_as_admin():
            sys.exit(0)  # Exit after requesting admin privileges
        else:
            print(f"{Fore.YELLOW}{EMOJI['INFO']} 需要管理员权限才能继续{Style.RESET_ALL}")
    
    # 导入 logo 模块
    try:
        from logo import print_logo
        print_logo()
    except ImportError:
        print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Cursor 机器 ID 重置工具{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
    
    # Initialize configuration
    config = get_config(translator)
    if not config:
        print(f"{Fore.RED}{EMOJI['ERROR']} 配置初始化失败{Style.RESET_ALL}")
        return
    force_update_config(translator)

    print_menu()
    
    while True:
        try:
            choice = input(f"\n{EMOJI['ARROW']} {Fore.CYAN}请输入选择 (0-1): {Style.RESET_ALL}")

            if choice == "0":
                print(f"\n{Fore.YELLOW}{EMOJI['INFO']} 退出中...{Style.RESET_ALL}")
                print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
                return
            elif choice == "1":
                    import reset_machine_manual
                    reset_machine_manual.run(translator)
                    print_menu()   
            else:
                print(f"{Fore.RED}{EMOJI['ERROR']} 无效的选择{Style.RESET_ALL}")
                print_menu()

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}{EMOJI['INFO']} 程序已终止{Style.RESET_ALL}")
            print(f"{Fore.CYAN}{'═' * 50}{Style.RESET_ALL}")
            return
        except Exception as e:
            print(f"{Fore.RED}{EMOJI['ERROR']} 发生错误: {str(e)}{Style.RESET_ALL}")
            print_menu()

if __name__ == "__main__":
    main()
