#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化订单验证对话框
"""

import sys
import os
import json
import platform
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QFrame, QApplication,
                            QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QColor, QPainter, QLinearGradient

class OrderVerifyThread(QThread):
    """订单验证线程"""
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    
    def __init__(self, order_number):
        super().__init__()
        self.order_number = order_number
    
    def run(self):
        """运行验证"""
        try:
            self.message.emit("验证订单格式...")
            self.progress.emit(25)
            
            # 验证订单号格式
            if not self.validate_order_format():
                self.finished.emit(False, "订单号格式不正确")
                return
            
            self.message.emit("检查本地记录...")
            self.progress.emit(50)
            
            # 检查本地黑名单
            if self.check_local_blacklist():
                self.finished.emit(False, "此订单号已被使用")
                return
            
            self.message.emit("验证订单有效性...")
            self.progress.emit(75)
            
            # 模拟验证过程
            import time
            time.sleep(1)
            
            self.message.emit("验证完成")
            self.progress.emit(100)
            
            # 保存订单
            self.save_order_to_blacklist()
            self.finished.emit(True, "订单验证成功！")
                
        except Exception as e:
            self.finished.emit(False, f"验证失败: {str(e)}")
    
    def validate_order_format(self):
        """验证订单号格式"""
        return len(self.order_number) == 19 and self.order_number.isdigit()
    
    def check_local_blacklist(self):
        """检查本地黑名单"""
        try:
            if platform.system() == "Darwin":
                blacklist_path = os.path.expanduser("~/blacklist1.json")
            else:
                blacklist_path = "C:/blacklist1.json"
            
            if os.path.exists(blacklist_path):
                with open(blacklist_path, 'r', encoding='utf-8') as f:
                    blacklist = json.load(f)
                    return self.order_number in blacklist
            return False
        except:
            return False
    
    def save_order_to_blacklist(self):
        """保存订单到黑名单"""
        try:
            if platform.system() == "Darwin":
                blacklist_path = os.path.expanduser("~/blacklist1.json")
            else:
                blacklist_path = "C:/blacklist1.json"
            
            blacklist = []
            if os.path.exists(blacklist_path):
                with open(blacklist_path, 'r', encoding='utf-8') as f:
                    blacklist = json.load(f)
            
            if self.order_number not in blacklist:
                blacklist.append(self.order_number)
                
                with open(blacklist_path, 'w', encoding='utf-8') as f:
                    json.dump(blacklist, f, indent=2)
        except:
            pass

class ModernLineEdit(QLineEdit):
    """现代化输入框"""
    
    def __init__(self, placeholder=""):
        super().__init__()
        self.setPlaceholderText(placeholder)
        self.setup_style()
    
    def setup_style(self):
        """设置样式"""
        self.setFixedHeight(50)
        self.setStyleSheet("""
            ModernLineEdit {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 12px;
                padding: 0 20px;
                font-size: 16px;
                font-weight: 500;
                color: #2c3e50;
            }
            ModernLineEdit:focus {
                border-color: #4A90E2;
                background: white;
            }
            ModernLineEdit:hover {
                border-color: #ced4da;
            }
        """)

class ModernOrderDialog(QDialog):
    """现代化订单验证对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.verify_thread = None
        self.init_ui()
        self.apply_style()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("订单验证")
        self.setFixedSize(400, 300)
        self.setModal(True)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("dialogContainer")
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)
        
        # 内容布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        
        # 标题区域
        self.create_header(layout)
        
        # 输入区域
        self.create_input_section(layout)
        
        # 状态区域
        self.create_status_section(layout)
        
        # 按钮区域
        self.create_button_section(layout)
        
        # 添加阴影
        self.add_shadow()
    
    def create_header(self, parent_layout):
        """创建标题区域"""
        header_layout = QVBoxLayout()
        header_layout.setSpacing(8)
        
        # 图标
        icon_label = QLabel("🔐")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px;")
        
        # 标题
        title_label = QLabel("订单验证")
        title_label.setObjectName("dialogTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 描述
        desc_label = QLabel("请输入19位数字订单号")
        desc_label.setObjectName("dialogDesc")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addWidget(desc_label)
        
        parent_layout.addLayout(header_layout)
    
    def create_input_section(self, parent_layout):
        """创建输入区域"""
        self.order_input = ModernLineEdit("输入19位订单号")
        self.order_input.setMaxLength(19)
        self.order_input.textChanged.connect(self.on_order_changed)
        
        parent_layout.addWidget(self.order_input)
    
    def create_status_section(self, parent_layout):
        """创建状态区域"""
        self.status_label = QLabel("等待输入订单号")
        self.status_label.setObjectName("statusText")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        parent_layout.addWidget(self.status_label)
    
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        # 取消按钮
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setObjectName("cancelButton")
        self.cancel_btn.setFixedHeight(45)
        self.cancel_btn.clicked.connect(self.reject)
        
        # 验证按钮
        self.verify_btn = QPushButton("验证")
        self.verify_btn.setObjectName("verifyButton")
        self.verify_btn.setFixedHeight(45)
        self.verify_btn.setEnabled(False)
        self.verify_btn.clicked.connect(self.start_verification)
        
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.verify_btn)
        
        parent_layout.addLayout(button_layout)
    
    def add_shadow(self):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 120))
        shadow.setOffset(0, 10)
        self.setGraphicsEffect(shadow)
    
    def apply_style(self):
        """应用样式"""
        self.setStyleSheet("""
            #dialogContainer {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-radius: 20px;
                border: 1px solid #e9ecef;
            }
            
            #dialogTitle {
                color: #2c3e50;
                font-size: 24px;
                font-weight: 700;
                font-family: 'Segoe UI', sans-serif;
            }
            
            #dialogDesc {
                color: #6c757d;
                font-size: 14px;
                font-weight: 400;
                font-family: 'Segoe UI', sans-serif;
            }
            
            #statusText {
                color: #495057;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Segoe UI', sans-serif;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
            
            #verifyButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4A90E2, stop:1 #357ABD);
                border: none;
                border-radius: 12px;
                color: white;
                font-size: 14px;
                font-weight: 600;
                font-family: 'Segoe UI', sans-serif;
            }
            
            #verifyButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5BA0F2, stop:1 #4A90E2);
            }
            
            #verifyButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #357ABD, stop:1 #2E6DA4);
            }
            
            #verifyButton:disabled {
                background: #ced4da;
                color: #6c757d;
            }
            
            #cancelButton {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 12px;
                color: #6c757d;
                font-size: 14px;
                font-weight: 600;
                font-family: 'Segoe UI', sans-serif;
            }
            
            #cancelButton:hover {
                background: #e9ecef;
                border-color: #ced4da;
                color: #495057;
            }
            
            #cancelButton:pressed {
                background: #dee2e6;
            }
        """)
    
    def on_order_changed(self, text):
        """订单号输入变化"""
        # 只允许数字
        filtered_text = ''.join(filter(str.isdigit, text))
        if filtered_text != text:
            self.order_input.setText(filtered_text)
            return
        
        # 检查长度并更新状态
        if len(filtered_text) == 19:
            self.verify_btn.setEnabled(True)
            self.status_label.setText("✅ 订单号格式正确，可以验证")
            self.status_label.setStyleSheet("""
                color: #28a745;
                font-size: 13px;
                font-weight: 500;
                padding: 10px;
                background: #d4edda;
                border-radius: 8px;
                border: 1px solid #c3e6cb;
            """)
        elif len(filtered_text) > 0:
            self.verify_btn.setEnabled(False)
            self.status_label.setText(f"⏳ 订单号长度: {len(filtered_text)}/19")
            self.status_label.setStyleSheet("""
                color: #ffc107;
                font-size: 13px;
                font-weight: 500;
                padding: 10px;
                background: #fff3cd;
                border-radius: 8px;
                border: 1px solid #ffeaa7;
            """)
        else:
            self.verify_btn.setEnabled(False)
            self.status_label.setText("等待输入订单号")
            self.status_label.setStyleSheet("""
                color: #495057;
                font-size: 13px;
                font-weight: 500;
                padding: 10px;
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            """)
    
    def start_verification(self):
        """开始验证"""
        order_number = self.order_input.text().strip()
        
        if len(order_number) != 19 or not order_number.isdigit():
            self.show_error("请输入19位数字订单号")
            return
        
        # 禁用输入和按钮
        self.order_input.setEnabled(False)
        self.verify_btn.setEnabled(False)
        self.verify_btn.setText("验证中...")
        
        # 启动验证线程
        self.verify_thread = OrderVerifyThread(order_number)
        self.verify_thread.finished.connect(self.on_verification_finished)
        self.verify_thread.message.connect(self.status_label.setText)
        self.verify_thread.start()
    
    def on_verification_finished(self, success, message):
        """验证完成"""
        if success:
            self.status_label.setText(f"✅ {message}")
            self.status_label.setStyleSheet("""
                color: #28a745;
                font-size: 13px;
                font-weight: 500;
                padding: 10px;
                background: #d4edda;
                border-radius: 8px;
                border: 1px solid #c3e6cb;
            """)
            
            # 延迟关闭对话框
            QTimer.singleShot(1500, self.accept)
        else:
            self.status_label.setText(f"❌ {message}")
            self.status_label.setStyleSheet("""
                color: #dc3545;
                font-size: 13px;
                font-weight: 500;
                padding: 10px;
                background: #f8d7da;
                border-radius: 8px;
                border: 1px solid #f5c6cb;
            """)
            
            # 重新启用输入
            self.order_input.setEnabled(True)
            self.verify_btn.setEnabled(True)
            self.verify_btn.setText("重试")
    
    def show_error(self, message):
        """显示错误"""
        self.status_label.setText(f"❌ {message}")
        self.status_label.setStyleSheet("""
            color: #dc3545;
            font-size: 13px;
            font-weight: 500;
            padding: 10px;
            background: #f8d7da;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
        """)

def show_modern_order_dialog(parent=None):
    """显示现代化订单验证对话框"""
    dialog = ModernOrderDialog(parent)
    return dialog.exec() == QDialog.DialogCode.Accepted

def main():
    """测试函数"""
    app = QApplication(sys.argv)
    
    # 设置字体
    font = QFont("Segoe UI", 9)
    app.setFont(font)
    
    dialog = ModernOrderDialog()
    
    # 居中显示
    screen = app.primaryScreen().geometry()
    dialog.move(
        (screen.width() - dialog.width()) // 2,
        (screen.height() - dialog.height()) // 2
    )
    
    result = dialog.exec()
    print(f"验证结果: {'成功' if result == QDialog.DialogCode.Accepted else '取消'}")

if __name__ == "__main__":
    main()
