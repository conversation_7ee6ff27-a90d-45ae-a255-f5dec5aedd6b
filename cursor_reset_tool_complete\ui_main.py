#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的PyQt6 GUI界面 - 主窗口
"""

import sys
import os
import threading
import time
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QTextEdit, 
                            QProgressBar, QTabWidget, QGroupBox, QGridLayout,
                            QMessageBox, QDialog, QLineEdit, QCheckBox,
                            QComboBox, QSpinBox, QFrame, QScrollArea)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QPixmap, QIcon, QPalette, QColor, QMovie
import json
from colorama import Fore, Style, init

# 导入我们的功能模块
try:
    import reset_machine_manual
    import email_manager
    import quit_cursor
    import delete_state_db
    import verify_order
    from config import get_config
except ImportError as e:
    print(f"导入模块失败: {e}")

# 初始化colorama
init()

class WorkerThread(QThread):
    """工作线程类"""
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    
    def __init__(self, task_type, *args, **kwargs):
        super().__init__()
        self.task_type = task_type
        self.args = args
        self.kwargs = kwargs
    
    def run(self):
        try:
            if self.task_type == "reset_machine":
                self.reset_machine_id()
            elif self.task_type == "email_manager":
                self.run_email_manager()
            elif self.task_type == "quit_cursor":
                self.quit_cursor_process()
            elif self.task_type == "delete_state":
                self.delete_state_db()
        except Exception as e:
            self.finished.emit(False, str(e))
    
    def reset_machine_id(self):
        """重置机器ID"""
        self.message.emit("正在初始化...")
        self.progress.emit(10)
        
        try:
            from reset_machine_manual import MachineIDResetter
            
            class SimpleTranslator:
                def get(self, key, **kwargs):
                    return key.format(**kwargs) if kwargs else key
            
            translator = SimpleTranslator()
            resetter = MachineIDResetter(translator)
            
            self.message.emit("正在生成新的机器ID...")
            self.progress.emit(30)
            
            result = resetter.reset_machine_ids()
            
            self.progress.emit(100)
            if result:
                self.finished.emit(True, "机器ID重置成功！")
            else:
                self.finished.emit(False, "机器ID重置失败")
                
        except Exception as e:
            self.finished.emit(False, f"重置过程出错: {str(e)}")
    
    def run_email_manager(self):
        """运行邮箱管理器"""
        self.message.emit("正在启动邮箱管理器...")
        self.progress.emit(20)
        
        try:
            manager = email_manager.EmailManager()
            
            self.message.emit("正在生成临时邮箱...")
            self.progress.emit(40)
            
            email = manager.generate_sub_email()
            
            self.message.emit(f"生成的邮箱: {email}")
            self.progress.emit(60)
            
            self.message.emit("邮箱管理器启动成功")
            self.progress.emit(100)
            self.finished.emit(True, f"临时邮箱已生成: {email}")
            
        except Exception as e:
            self.finished.emit(False, f"邮箱管理器错误: {str(e)}")
    
    def quit_cursor_process(self):
        """关闭Cursor进程"""
        self.message.emit("正在查找Cursor进程...")
        self.progress.emit(30)
        
        try:
            class SimpleTranslator:
                def get(self, key, **kwargs):
                    return key.format(**kwargs) if kwargs else key
            
            translator = SimpleTranslator()
            
            self.message.emit("正在关闭Cursor进程...")
            self.progress.emit(70)
            
            quit_cursor.quit_cursor(translator)
            
            self.progress.emit(100)
            self.finished.emit(True, "Cursor进程已关闭")
            
        except Exception as e:
            self.finished.emit(False, f"关闭进程错误: {str(e)}")
    
    def delete_state_db(self):
        """删除状态数据库"""
        self.message.emit("正在查找状态数据库...")
        self.progress.emit(30)
        
        try:
            self.message.emit("正在删除状态数据库...")
            self.progress.emit(70)
            
            delete_state_db.delete_state_db()
            
            self.progress.emit(100)
            self.finished.emit(True, "状态数据库已清理")
            
        except Exception as e:
            self.finished.emit(False, f"清理数据库错误: {str(e)}")

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.worker_thread = None
        self.init_ui()
        self.apply_dark_theme()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("Cursor Pro 无限续杯工具 v2.2.3")
        self.setGeometry(100, 100, 800, 600)
        self.setMinimumSize(700, 500)
        
        # 设置窗口图标
        try:
            if os.path.exists("image/logo.png"):
                self.setWindowIcon(QIcon("image/logo.png"))
        except:
            pass
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题区域
        self.create_header(main_layout)
        
        # 创建选项卡
        self.create_tabs(main_layout)
        
        # 创建状态栏
        self.create_status_bar(main_layout)
        
    def create_header(self, parent_layout):
        """创建标题区域"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.Box)
        header_frame.setMaximumHeight(120)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Logo区域
        logo_label = QLabel()
        try:
            if os.path.exists("image/logo.png"):
                pixmap = QPixmap("image/logo.png")
                scaled_pixmap = pixmap.scaled(80, 80, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                logo_label.setPixmap(scaled_pixmap)
            else:
                logo_label.setText("🎯")
                logo_label.setStyleSheet("font-size: 48px;")
        except:
            logo_label.setText("🎯")
            logo_label.setStyleSheet("font-size: 48px;")
        
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 标题文本
        title_layout = QVBoxLayout()
        
        title_label = QLabel("Cursor Pro 无限续杯工具")
        title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #00d4aa;")
        
        version_label = QLabel("Pro Version Activator v2.2.3")
        version_label.setStyleSheet("font-size: 14px; color: #888;")
        
        author_label = QLabel("Author: Pin Studios (yeongpin)")
        author_label.setStyleSheet("font-size: 12px; color: #666;")
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(version_label)
        title_layout.addWidget(author_label)
        title_layout.addStretch()
        
        header_layout.addWidget(logo_label)
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        parent_layout.addWidget(header_frame)
    
    def create_tabs(self, parent_layout):
        """创建选项卡"""
        self.tab_widget = QTabWidget()
        
        # 主要功能选项卡
        self.create_main_tab()
        
        # 邮箱管理选项卡
        self.create_email_tab()
        
        # 高级功能选项卡
        self.create_advanced_tab()
        
        # 关于选项卡
        self.create_about_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def create_main_tab(self):
        """创建主要功能选项卡"""
        main_tab = QWidget()
        layout = QVBoxLayout(main_tab)
        
        # 功能按钮组
        buttons_group = QGroupBox("主要功能")
        buttons_layout = QGridLayout(buttons_group)
        
        # 重置机器ID按钮
        reset_btn = QPushButton("🔄 重置 Cursor 机器 ID")
        reset_btn.setMinimumHeight(50)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #00d4aa;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #00b894;
            }
            QPushButton:pressed {
                background-color: #00a085;
            }
        """)
        reset_btn.clicked.connect(self.reset_machine_id)
        
        # 邮箱管理按钮
        email_btn = QPushButton("📧 邮箱管理工具")
        email_btn.setMinimumHeight(50)
        email_btn.setStyleSheet("""
            QPushButton {
                background-color: #0984e3;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0770c4;
            }
            QPushButton:pressed {
                background-color: #065fa5;
            }
        """)
        email_btn.clicked.connect(self.open_email_manager)
        
        # 关闭进程按钮
        quit_btn = QPushButton("⚙️ 关闭 Cursor 进程")
        quit_btn.setMinimumHeight(50)
        quit_btn.setStyleSheet("""
            QPushButton {
                background-color: #e17055;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d63031;
            }
            QPushButton:pressed {
                background-color: #c0392b;
            }
        """)
        quit_btn.clicked.connect(self.quit_cursor)
        
        # 清理数据库按钮
        clean_btn = QPushButton("🧹 清理状态数据库")
        clean_btn.setMinimumHeight(50)
        clean_btn.setStyleSheet("""
            QPushButton {
                background-color: #a29bfe;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8b7ff5;
            }
            QPushButton:pressed {
                background-color: #7b6ef0;
            }
        """)
        clean_btn.clicked.connect(self.delete_state_db)
        
        buttons_layout.addWidget(reset_btn, 0, 0)
        buttons_layout.addWidget(email_btn, 0, 1)
        buttons_layout.addWidget(quit_btn, 1, 0)
        buttons_layout.addWidget(clean_btn, 1, 1)
        
        layout.addWidget(buttons_group)
        layout.addStretch()
        
        self.tab_widget.addTab(main_tab, "🏠 主要功能")
    
    def create_email_tab(self):
        """创建邮箱管理选项卡"""
        email_tab = QWidget()
        layout = QVBoxLayout(email_tab)
        
        # 邮箱信息组
        email_group = QGroupBox("邮箱信息")
        email_layout = QVBoxLayout(email_group)
        
        self.email_display = QTextEdit()
        self.email_display.setMaximumHeight(100)
        self.email_display.setPlaceholderText("生成的临时邮箱将显示在这里...")
        self.email_display.setReadOnly(True)
        
        email_layout.addWidget(self.email_display)
        
        # 验证码显示组
        code_group = QGroupBox("验证码")
        code_layout = QVBoxLayout(code_group)
        
        self.code_display = QTextEdit()
        self.code_display.setMaximumHeight(100)
        self.code_display.setPlaceholderText("接收到的验证码将显示在这里...")
        self.code_display.setReadOnly(True)
        
        code_layout.addWidget(self.code_display)
        
        layout.addWidget(email_group)
        layout.addWidget(code_group)
        layout.addStretch()
        
        self.tab_widget.addTab(email_tab, "📧 邮箱管理")
    
    def create_advanced_tab(self):
        """创建高级功能选项卡"""
        advanced_tab = QWidget()
        layout = QVBoxLayout(advanced_tab)
        
        # 配置信息组
        config_group = QGroupBox("配置信息")
        config_layout = QVBoxLayout(config_group)
        
        self.config_display = QTextEdit()
        self.config_display.setMaximumHeight(200)
        self.config_display.setReadOnly(True)
        
        # 加载配置信息
        self.load_config_info()
        
        config_layout.addWidget(self.config_display)
        
        # 订单验证按钮
        verify_btn = QPushButton("💰 订单验证")
        verify_btn.setMinimumHeight(40)
        verify_btn.setStyleSheet("""
            QPushButton {
                background-color: #fdcb6e;
                color: #2d3436;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f39c12;
            }
        """)
        verify_btn.clicked.connect(self.verify_order)
        
        layout.addWidget(config_group)
        layout.addWidget(verify_btn)
        layout.addStretch()
        
        self.tab_widget.addTab(advanced_tab, "⚙️ 高级功能")
    
    def create_about_tab(self):
        """创建关于选项卡"""
        about_tab = QWidget()
        layout = QVBoxLayout(about_tab)
        
        about_text = QTextEdit()
        about_text.setReadOnly(True)
        about_text.setHtml("""
        <div style="text-align: center; padding: 20px;">
            <h2 style="color: #00d4aa;">Cursor Pro 无限续杯工具</h2>
            <h3>Pro Version Activator v2.2.3</h3>
            
            <p><strong>作者:</strong> Pin Studios (yeongpin)</p>
            <p><strong>GitHub:</strong> <a href="https://github.com/yeongpin/cursor-free-vip">https://github.com/yeongpin/cursor-free-vip</a></p>
            
            <h4>功能特性:</h4>
            <ul style="text-align: left;">
                <li>🔄 重置 Cursor 机器 ID</li>
                <li>📧 临时邮箱管理系统</li>
                <li>⚙️ 进程管理功能</li>
                <li>🧹 状态数据库清理</li>
                <li>💰 订单验证系统</li>
                <li>🎨 现代化GUI界面</li>
            </ul>
            
            <h4>贡献者:</h4>
            <p>BasaiCorp, aliensb, handwerk2016, Nigel1992, UntaDotMy, RenjiYuusei, imbajin, ahmed98Osama, bingoohuang, mALIk-sHAHId, MFaiqKhan, httpmerak, muhammedfurkan, plamkatawe, Lucaszmv</p>
            
            <p style="color: #e74c3c; font-weight: bold;">⚠️ 本工具仅供学习研究使用</p>
        </div>
        """)
        
        layout.addWidget(about_text)
        
        self.tab_widget.addTab(about_tab, "ℹ️ 关于")
    
    def create_status_bar(self, parent_layout):
        """创建状态栏"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Shape.Box)
        status_frame.setMaximumHeight(80)
        
        status_layout = QVBoxLayout(status_frame)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #00d4aa; font-weight: bold;")
        
        status_layout.addWidget(self.progress_bar)
        status_layout.addWidget(self.status_label)
        
        parent_layout.addWidget(status_frame)
    
    def load_config_info(self):
        """加载配置信息"""
        try:
            config = get_config()
            if config:
                config_text = "当前配置信息:\n\n"
                
                for section in config.sections():
                    config_text += f"[{section}]\n"
                    for key, value in config.items(section):
                        config_text += f"  {key} = {value}\n"
                    config_text += "\n"
                
                self.config_display.setPlainText(config_text)
            else:
                self.config_display.setPlainText("配置加载失败")
        except Exception as e:
            self.config_display.setPlainText(f"配置加载错误: {str(e)}")
    
    def apply_dark_theme(self):
        """应用深色主题"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            QWidget {
                background-color: #2c3e50;
                color: #ecf0f1;
            }
            QTabWidget::pane {
                border: 1px solid #34495e;
                background-color: #34495e;
            }
            QTabBar::tab {
                background-color: #34495e;
                color: #ecf0f1;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #00d4aa;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #4a6741;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #34495e;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #00d4aa;
            }
            QTextEdit {
                background-color: #34495e;
                border: 1px solid #4a6741;
                border-radius: 4px;
                padding: 5px;
                color: #ecf0f1;
            }
            QProgressBar {
                border: 1px solid #34495e;
                border-radius: 4px;
                text-align: center;
                background-color: #34495e;
            }
            QProgressBar::chunk {
                background-color: #00d4aa;
                border-radius: 3px;
            }
            QFrame {
                background-color: #34495e;
                border-radius: 8px;
            }
        """)
    
    def show_progress(self, show=True):
        """显示/隐藏进度条"""
        self.progress_bar.setVisible(show)
        if not show:
            self.progress_bar.setValue(0)
    
    def update_status(self, message):
        """更新状态信息"""
        self.status_label.setText(message)
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def on_task_finished(self, success, message):
        """任务完成回调"""
        self.show_progress(False)
        
        if success:
            self.update_status("✅ " + message)
            QMessageBox.information(self, "成功", message)
        else:
            self.update_status("❌ " + message)
            QMessageBox.critical(self, "错误", message)
        
        # 重置状态
        QTimer.singleShot(3000, lambda: self.update_status("就绪"))
    
    def reset_machine_id(self):
        """重置机器ID"""
        reply = QMessageBox.question(self, "确认", "确定要重置Cursor机器ID吗？\n这将生成新的设备标识符。", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            self.show_progress(True)
            self.update_status("正在重置机器ID...")
            
            self.worker_thread = WorkerThread("reset_machine")
            self.worker_thread.finished.connect(self.on_task_finished)
            self.worker_thread.progress.connect(self.update_progress)
            self.worker_thread.message.connect(self.update_status)
            self.worker_thread.start()
    
    def open_email_manager(self):
        """打开邮箱管理器"""
        self.show_progress(True)
        self.update_status("正在启动邮箱管理器...")
        
        self.worker_thread = WorkerThread("email_manager")
        self.worker_thread.finished.connect(self.on_email_finished)
        self.worker_thread.progress.connect(self.update_progress)
        self.worker_thread.message.connect(self.update_status)
        self.worker_thread.start()
    
    def on_email_finished(self, success, message):
        """邮箱任务完成回调"""
        self.show_progress(False)
        
        if success:
            self.update_status("✅ " + message)
            # 在邮箱选项卡中显示结果
            self.email_display.setPlainText(message)
            # 切换到邮箱选项卡
            self.tab_widget.setCurrentIndex(1)
        else:
            self.update_status("❌ " + message)
            QMessageBox.critical(self, "错误", message)
        
        QTimer.singleShot(3000, lambda: self.update_status("就绪"))
    
    def quit_cursor(self):
        """关闭Cursor进程"""
        reply = QMessageBox.question(self, "确认", "确定要关闭所有Cursor进程吗？", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            self.show_progress(True)
            self.update_status("正在关闭Cursor进程...")
            
            self.worker_thread = WorkerThread("quit_cursor")
            self.worker_thread.finished.connect(self.on_task_finished)
            self.worker_thread.progress.connect(self.update_progress)
            self.worker_thread.message.connect(self.update_status)
            self.worker_thread.start()
    
    def delete_state_db(self):
        """删除状态数据库"""
        reply = QMessageBox.question(self, "确认", "确定要清理Cursor状态数据库吗？\n这将删除相关的状态文件。", 
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            self.show_progress(True)
            self.update_status("正在清理状态数据库...")
            
            self.worker_thread = WorkerThread("delete_state")
            self.worker_thread.finished.connect(self.on_task_finished)
            self.worker_thread.progress.connect(self.update_progress)
            self.worker_thread.message.connect(self.update_status)
            self.worker_thread.start()
    
    def verify_order(self):
        """验证订单"""
        try:
            from ui_order_verify import show_order_verify_dialog
            success = show_order_verify_dialog(self)
            if success:
                self.update_status("✅ 订单验证成功")
            else:
                self.update_status("订单验证已取消")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"订单验证错误: {str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("Cursor Pro 无限续杯工具")
    app.setApplicationVersion("2.2.3")
    app.setOrganizationName("Pin Studios")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
