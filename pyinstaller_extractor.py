#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller提取器
用于从PyInstaller打包的exe文件中提取Python源代码
"""

import os
import sys
import struct
import zlib
import marshal
import py_compile
import tempfile
import shutil

class PyInstaller_Extractor:
    def __init__(self, filename):
        self.filename = filename
        self.pylib_name = None
        self.cookie = None
        self.toc = None
        self.overlay_size = None
        self.overlay_pos = None
        self.pyz = None
        
    def check_file(self):
        """检查文件是否为PyInstaller打包文件"""
        try:
            with open(self.filename, 'rb') as f:
                # 查找PyInstaller cookie
                f.seek(-4096, 2)  # 从文件末尾开始搜索
                data = f.read()
                
                # 查找PyInstaller magic
                magic_patterns = [
                    b'MEI\x0c\x0b\x0a\x0b\x0e',
                    b'PYZ-00',
                    b'python'
                ]
                
                for pattern in magic_patterns:
                    if pattern in data:
                        print(f"✓ 发现PyInstaller特征: {pattern}")
                        return True
                        
                return False
        except Exception as e:
            print(f"检查文件时出错: {e}")
            return False
    
    def find_overlay(self):
        """查找overlay数据"""
        try:
            with open(self.filename, 'rb') as f:
                # 读取文件末尾寻找cookie
                f.seek(-4096, 2)
                data = f.read()
                
                # 查找可能的cookie位置
                for i in range(len(data) - 24, -1, -1):
                    if data[i:i+8] == b'MEI\x0c\x0b\x0a\x0b\x0e':
                        cookie_pos = f.tell() - len(data) + i
                        f.seek(cookie_pos + 8)
                        
                        # 读取cookie数据
                        cookie_data = f.read(16)
                        if len(cookie_data) == 16:
                            self.overlay_size = struct.unpack('<I', cookie_data[0:4])[0]
                            self.overlay_pos = struct.unpack('<I', cookie_data[4:8])[0]
                            print(f"✓ 找到overlay: 位置=0x{self.overlay_pos:08X}, 大小={self.overlay_size}")
                            return True
                            
                return False
        except Exception as e:
            print(f"查找overlay时出错: {e}")
            return False
    
    def extract_toc(self):
        """提取目录表"""
        if not self.overlay_pos or not self.overlay_size:
            return False
            
        try:
            with open(self.filename, 'rb') as f:
                f.seek(self.overlay_pos)
                overlay_data = f.read(self.overlay_size)
                
                # 尝试解析TOC
                if len(overlay_data) > 12:
                    # 简单的TOC解析
                    toc_pos = 0
                    self.toc = []
                    
                    while toc_pos < len(overlay_data) - 12:
                        try:
                            # 尝试读取条目
                            entry_len = struct.unpack('<I', overlay_data[toc_pos:toc_pos+4])[0]
                            if entry_len > 1000 or entry_len < 4:
                                break
                                
                            entry_data = overlay_data[toc_pos+4:toc_pos+4+entry_len]
                            if len(entry_data) >= entry_len:
                                # 尝试解析文件名
                                name_end = entry_data.find(b'\x00')
                                if name_end > 0:
                                    name = entry_data[:name_end].decode('utf-8', errors='ignore')
                                    if name.endswith('.py') or name.endswith('.pyc'):
                                        self.toc.append(name)
                                        print(f"  发现文件: {name}")
                            
                            toc_pos += 4 + entry_len
                            
                            if len(self.toc) > 100:  # 限制数量
                                break
                                
                        except:
                            toc_pos += 1
                            
                    return len(self.toc) > 0
                    
        except Exception as e:
            print(f"提取TOC时出错: {e}")
            return False
    
    def extract_files(self, output_dir):
        """提取文件到指定目录"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        extracted_count = 0
        
        try:
            with open(self.filename, 'rb') as f:
                # 搜索可能的Python字节码
                f.seek(0)
                content = f.read()
                
                # 查找Python magic number
                python_magics = [
                    b'\x42\x0d\x0d\x0a',  # Python 3.8
                    b'\x55\x0d\x0d\x0a',  # Python 3.9
                    b'\x6f\x0d\x0d\x0a',  # Python 3.10
                    b'\xa7\x0d\x0d\x0a',  # Python 3.11
                ]
                
                for magic in python_magics:
                    pos = 0
                    while True:
                        pos = content.find(magic, pos)
                        if pos == -1:
                            break
                            
                        try:
                            # 尝试提取marshal数据
                            f.seek(pos + 16)  # 跳过magic和时间戳
                            size_data = f.read(4)
                            if len(size_data) == 4:
                                size = struct.unpack('<I', size_data)[0]
                                if 100 < size < 1000000:  # 合理的大小范围
                                    marshal_data = f.read(size)
                                    if len(marshal_data) == size:
                                        try:
                                            code_obj = marshal.loads(marshal_data)
                                            if hasattr(code_obj, 'co_filename'):
                                                filename = os.path.basename(code_obj.co_filename)
                                                if filename and filename != '<string>':
                                                    output_file = os.path.join(output_dir, f"{filename}_{extracted_count}.py")
                                                    self.decompile_code_object(code_obj, output_file)
                                                    extracted_count += 1
                                                    print(f"✓ 提取: {filename}")
                                        except:
                                            pass
                        except:
                            pass
                            
                        pos += 1
                        if extracted_count > 20:  # 限制提取数量
                            break
                            
                    if extracted_count > 20:
                        break
                        
        except Exception as e:
            print(f"提取文件时出错: {e}")
            
        return extracted_count
    
    def decompile_code_object(self, code_obj, output_file):
        """反编译代码对象"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# 反编译自: {code_obj.co_filename}\n")
                f.write(f"# 函数名: {code_obj.co_name}\n")
                f.write(f"# 参数数量: {code_obj.co_argcount}\n")
                f.write(f"# 局部变量: {code_obj.co_varnames}\n")
                f.write(f"# 常量: {code_obj.co_consts}\n")
                f.write(f"# 名称: {code_obj.co_names}\n\n")
                
                # 尝试重建基本结构
                if code_obj.co_names:
                    f.write("# 导入的模块/名称:\n")
                    for name in code_obj.co_names:
                        if isinstance(name, str):
                            f.write(f"# - {name}\n")
                
                f.write("\n# 字节码信息:\n")
                f.write(f"# 字节码长度: {len(code_obj.co_code)}\n")
                
                # 如果有字符串常量，可能包含有用信息
                if code_obj.co_consts:
                    f.write("\n# 字符串常量:\n")
                    for const in code_obj.co_consts:
                        if isinstance(const, str) and len(const) > 3:
                            f.write(f'# "{const}"\n')
                            
        except Exception as e:
            print(f"反编译代码对象时出错: {e}")

def main():
    filename = "无线续杯Win系统v2.2.3版本.exe"
    output_dir = "extracted_source"
    
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return
    
    print(f"PyInstaller提取器")
    print(f"目标文件: {filename}")
    print("=" * 60)
    
    extractor = PyInstaller_Extractor(filename)
    
    # 检查文件
    if not extractor.check_file():
        print("❌ 这不是一个PyInstaller打包的文件")
        return
    
    print("✓ 确认为PyInstaller打包文件")
    
    # 查找overlay
    if extractor.find_overlay():
        print("✓ 找到overlay数据")
        
        # 提取TOC
        if extractor.extract_toc():
            print(f"✓ 找到 {len(extractor.toc)} 个文件条目")
    
    # 提取文件
    print(f"\n开始提取到目录: {output_dir}")
    extracted = extractor.extract_files(output_dir)
    
    if extracted > 0:
        print(f"✓ 成功提取 {extracted} 个文件")
        print(f"请查看 {output_dir} 目录")
    else:
        print("❌ 未能提取到文件")

if __name__ == "__main__":
    main()
