#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全仿照原始软件的启动器
"""

import sys
import os
import platform
from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QPixmap, QFont
from colorama import Fore, Style, init

# 初始化colorama
init()

def check_admin_privileges():
    """检查管理员权限"""
    if platform.system() == 'Windows':
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False
    return True

def request_admin_privileges():
    """请求管理员权限"""
    if platform.system() != 'Windows':
        return False
        
    try:
        import ctypes
        args = [sys.executable] + sys.argv
        
        # Request elevation via ShellExecute
        ctypes.windll.shell32.ShellExecuteW(None, "runas", args[0], " ".join('"' + arg + '"' for arg in args[1:]), None, 1)
        return True
    except Exception as e:
        print(f"无法以管理员权限重启: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    missing_deps = []
    
    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import colorama
    except ImportError:
        missing_deps.append("colorama")
    
    try:
        import psutil
    except ImportError:
        missing_deps.append("psutil")
    
    return missing_deps

def main():
    """主函数"""
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}🎯 Cursor Free VIP v2.2.3 - 原始风格版本{Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
    
    # 检查依赖
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"{Fore.RED}❌ 缺少依赖包: {', '.join(missing_deps)}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}请运行: pip install {' '.join(missing_deps)}{Style.RESET_ALL}")
        input("按回车键退出...")
        return
    
    # 检查管理员权限（仅Windows）
    if platform.system() == 'Windows' and not check_admin_privileges():
        print(f"{Fore.YELLOW}⚠️  需要管理员权限{Style.RESET_ALL}")
        if request_admin_privileges():
            print(f"{Fore.CYAN}正在以管理员权限重启...{Style.RESET_ALL}")
            sys.exit(0)
        else:
            print(f"{Fore.YELLOW}⚠️  无法获取管理员权限，某些功能可能无法正常工作{Style.RESET_ALL}")
    
    # 创建QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Cursor Free VIP")
    app.setApplicationVersion("2.2.3")
    app.setOrganizationName("Pin Studios")
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    try:
        print(f"{Fore.CYAN}📋 正在进行订单验证...{Style.RESET_ALL}")
        
        # 导入订单验证模块
        from ui_order_original import show_original_order_dialog
        
        # 显示订单验证对话框
        order_verified = show_original_order_dialog()
        
        if not order_verified:
            print(f"{Fore.RED}❌ 订单验证失败或被取消{Style.RESET_ALL}")
            return
        
        print(f"{Fore.GREEN}✅ 订单验证成功{Style.RESET_ALL}")
        
        # 导入主界面模块
        from ui_original_style import OriginalMainWindow
        
        # 创建主窗口
        print(f"{Fore.CYAN}🚀 正在启动主界面...{Style.RESET_ALL}")
        main_window = OriginalMainWindow()
        
        main_window.show()
        
        print(f"{Fore.GREEN}✅ 原始风格GUI界面启动成功！{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except ImportError as e:
        error_msg = f"GUI模块导入失败: {str(e)}\n\n请确保已安装PyQt6:\npip install PyQt6"
        print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
        
        # 尝试显示错误对话框
        try:
            QMessageBox.critical(None, "导入错误", error_msg)
        except:
            pass
        
        # 回退到控制台版本
        print(f"{Fore.YELLOW}🔄 正在启动控制台版本...{Style.RESET_ALL}")
        try:
            from main_console import main as console_main
            console_main()
        except Exception as console_error:
            print(f"{Fore.RED}❌ 控制台版本也启动失败: {console_error}{Style.RESET_ALL}")
            input("按回车键退出...")
        
    except Exception as e:
        error_msg = f"程序启动失败: {str(e)}"
        print(f"{Fore.RED}❌ {error_msg}{Style.RESET_ALL}")
        
        try:
            QMessageBox.critical(None, "启动错误", error_msg)
        except:
            pass
        
        print(f"{Fore.YELLOW}🔄 正在启动控制台版本...{Style.RESET_ALL}")
        try:
            from main_console import main as console_main
            console_main()
        except Exception as console_error:
            print(f"{Fore.RED}❌ 控制台版本也启动失败: {console_error}{Style.RESET_ALL}")
            input("按回车键退出...")

if __name__ == "__main__":
    main()
