#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PE文件分析工具
用于分析Windows PE文件的基本信息
"""

import os
import sys
import struct
import binascii

def analyze_pe_file(filename):
    """分析PE文件的基本信息"""
    try:
        with open(filename, 'rb') as f:
            # 读取DOS头
            dos_header = f.read(64)
            if len(dos_header) < 64:
                print("文件太小，不是有效的PE文件")
                return
            
            # 检查DOS签名
            if dos_header[:2] != b'MZ':
                print("不是有效的PE文件（缺少MZ签名）")
                return
            
            # 获取PE头偏移
            pe_offset = struct.unpack('<L', dos_header[60:64])[0]
            print(f"文件大小: {os.path.getsize(filename)} 字节")
            print(f"PE头偏移: 0x{pe_offset:08X}")
            
            # 跳转到PE头
            f.seek(pe_offset)
            pe_signature = f.read(4)
            
            if pe_signature != b'PE\x00\x00':
                print("不是有效的PE文件（缺少PE签名）")
                return
            
            print("✓ 这是一个有效的PE文件")
            
            # 读取COFF头
            coff_header = f.read(20)
            machine = struct.unpack('<H', coff_header[0:2])[0]
            num_sections = struct.unpack('<H', coff_header[2:4])[0]
            timestamp = struct.unpack('<L', coff_header[4:8])[0]
            
            # 机器类型
            machine_types = {
                0x014c: "i386",
                0x0200: "Intel Itanium",
                0x8664: "x64"
            }
            
            print(f"目标机器: {machine_types.get(machine, f'Unknown (0x{machine:04X})')}")
            print(f"节数量: {num_sections}")
            print(f"时间戳: {timestamp} ({hex(timestamp)})")
            
            # 读取可选头
            optional_header_size = struct.unpack('<H', coff_header[16:18])[0]
            optional_header = f.read(optional_header_size)
            
            if len(optional_header) >= 2:
                magic = struct.unpack('<H', optional_header[0:2])[0]
                if magic == 0x10b:
                    print("PE类型: PE32")
                elif magic == 0x20b:
                    print("PE类型: PE32+")
                else:
                    print(f"未知PE类型: 0x{magic:04X}")
            
            # 尝试提取字符串
            print("\n=== 尝试提取可见字符串 ===")
            f.seek(0)
            content = f.read()
            
            # 提取ASCII字符串
            strings = []
            current_string = ""
            
            for byte in content:
                if 32 <= byte <= 126:  # 可打印ASCII字符
                    current_string += chr(byte)
                else:
                    if len(current_string) >= 4:  # 只保留长度>=4的字符串
                        strings.append(current_string)
                    current_string = ""
            
            # 显示前20个有意义的字符串
            interesting_strings = []
            for s in strings[:50]:  # 检查前50个字符串
                if any(keyword in s.lower() for keyword in ['http', 'www', 'exe', 'dll', 'sys', 'version', 'copyright', 'microsoft', 'windows']):
                    interesting_strings.append(s)
            
            print("发现的有趣字符串:")
            for i, s in enumerate(interesting_strings[:20]):
                print(f"  {i+1:2d}: {s}")
            
            if len(strings) > 20:
                print(f"... 还有 {len(strings)-20} 个字符串")
                
    except Exception as e:
        print(f"分析文件时出错: {e}")

def main():
    filename = "无线续杯Win系统v2.2.3版本.exe"
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return
    
    print(f"正在分析文件: {filename}")
    print("=" * 50)
    analyze_pe_file(filename)

if __name__ == "__main__":
    main()
