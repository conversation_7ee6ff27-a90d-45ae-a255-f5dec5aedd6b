#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建脚本 - 用于打包 Cursor 重置工具
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, cwd=None):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"错误: {result.stderr}")
            return False
        print(result.stdout)
        return True
    except Exception as e:
        print(f"执行命令失败: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    return run_command("pip install -r requirements.txt")

def install_pyinstaller():
    """安装 PyInstaller"""
    print("正在安装 PyInstaller...")
    return run_command("pip install pyinstaller")

def build_executable():
    """构建可执行文件"""
    print("正在构建可执行文件...")
    
    # PyInstaller 命令
    cmd = [
        "pyinstaller",
        "--onefile",                                    # 打包成单个文件
        "--noconsole",                                  # 不显示控制台窗口
        "--name", "无线续杯Win系统v2.2.3版本-完整版",        # 输出文件名
        "--add-data", "logo.py;.",                      # 包含 logo.py
        "--hidden-import", "colorama",                  # 隐式导入
        "--hidden-import", "psutil",                    # 隐式导入
        "--hidden-import", "configparser",              # 隐式导入
        "--hidden-import", "sqlite3",                   # 隐式导入
        "--hidden-import", "uuid",                      # 隐式导入
        "--hidden-import", "hashlib",                   # 隐式导入
        "--hidden-import", "json",                      # 隐式导入
        "--hidden-import", "shutil",                    # 隐式导入
        "--hidden-import", "tempfile",                  # 隐式导入
        "--hidden-import", "glob",                      # 隐式导入
        "--hidden-import", "re",                        # 隐式导入
        "--hidden-import", "traceback",                 # 隐式导入
        "--hidden-import", "datetime",                  # 隐式导入
        "--hidden-import", "platform",                  # 隐式导入
        "--hidden-import", "locale",                    # 隐式导入
        "--hidden-import", "winreg",                    # Windows 注册表
        "--hidden-import", "PyQt6.QtWidgets",           # PyQt6 组件
        "--hidden-import", "PyQt6.QtCore",              # PyQt6 核心
        "--hidden-import", "PyQt6.QtGui",               # PyQt6 GUI
        "--hidden-import", "requests",                   # HTTP 请求
        "--hidden-import", "poplib",                     # POP3 邮件
        "--hidden-import", "email",                      # 邮件处理
        "--hidden-import", "threading",                  # 线程
        "--hidden-import", "subprocess",                 # 子进程
        "--hidden-import", "base64",                     # Base64编码
        "--hidden-import", "marshal",                    # 序列化
        "main.py"                                       # 主文件
    ]
    
    return run_command(" ".join(cmd))

def clean_build_files():
    """清理构建文件"""
    print("正在清理构建文件...")
    
    # 要删除的目录和文件
    to_remove = [
        "build",
        "dist",
        "__pycache__",
        "*.spec"
    ]
    
    for item in to_remove:
        if item.startswith("*"):
            # 处理通配符
            import glob
            for file in glob.glob(item):
                try:
                    os.remove(file)
                    print(f"已删除文件: {file}")
                except Exception as e:
                    print(f"删除文件失败 {file}: {e}")
        else:
            # 处理目录
            if os.path.exists(item):
                try:
                    shutil.rmtree(item)
                    print(f"已删除目录: {item}")
                except Exception as e:
                    print(f"删除目录失败 {item}: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("Cursor 重置工具构建脚本")
    print("=" * 60)
    
    # 检查是否在正确的目录
    if not os.path.exists("main.py"):
        print("错误: 请在包含 main.py 的目录中运行此脚本")
        sys.exit(1)
    
    # 步骤1: 安装依赖
    if not install_dependencies():
        print("安装依赖失败")
        sys.exit(1)
    
    # 步骤2: 安装 PyInstaller
    if not install_pyinstaller():
        print("安装 PyInstaller 失败")
        sys.exit(1)
    
    # 步骤3: 清理旧的构建文件
    clean_build_files()
    
    # 步骤4: 构建可执行文件
    if not build_executable():
        print("构建可执行文件失败")
        sys.exit(1)
    
    # 步骤5: 检查输出文件
    exe_path = os.path.join("dist", "无线续杯Win系统v2.2.3版本.exe")
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print(f"\n✅ 构建成功!")
        print(f"📁 输出文件: {exe_path}")
        print(f"📊 文件大小: {file_size:.2f} MB")
        print(f"\n🎉 可执行文件已生成，可以分发使用了！")
    else:
        print("\n❌ 构建失败: 找不到输出文件")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("构建完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
