#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试2925邮箱服务连接
"""

import poplib
import sys

def test_2925_connection():
    """测试2925邮箱POP3连接"""
    
    # POP3服务器配置
    POP3_SERVER = 'pop.2925.com'
    POP3_PORT = 995
    POP3_SSL = True
    
    # 测试账号
    test_accounts = [
        {'email': 'maonai<PERSON><EMAIL>', 'password': 'mnysb123.'},
        {'email': '<EMAIL>', 'password': 'mnysb123.'},
        {'email': '<EMAIL>', 'password': 'mnysb123.'},
        {'email': '<EMAIL>', 'password': 'mnysb123.'},
        {'email': '<EMAIL>', 'password': 'mnysb666.'}
    ]
    
    print("🔄 开始测试2925邮箱POP3连接...")
    print(f"服务器: {POP3_SERVER}:{POP3_PORT} (SSL: {POP3_SSL})")
    print("="*60)
    
    successful_accounts = []
    failed_accounts = []
    
    for i, account in enumerate(test_accounts, 1):
        print(f"\n[{i}/{len(test_accounts)}] 测试账号: {account['email']}")
        
        try:
            # 连接到POP3服务器
            if POP3_SSL:
                mail_server = poplib.POP3_SSL(POP3_SERVER, POP3_PORT)
            else:
                mail_server = poplib.POP3(POP3_SERVER, POP3_PORT)
            
            print("  ✅ POP3连接成功")
            
            # 登录
            mail_server.user(account['email'])
            mail_server.pass_(account['password'])
            
            print("  ✅ 登录成功")
            
            # 获取邮件数量
            email_count = len(mail_server.list()[1])
            print(f"  📧 邮件数量: {email_count}")
            
            # 关闭连接
            mail_server.quit()
            print("  ✅ 连接关闭")
            
            successful_accounts.append(account['email'])
            
        except Exception as e:
            print(f"  ❌ 连接失败: {str(e)}")
            failed_accounts.append((account['email'], str(e)))
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试结果总结:")
    print(f"✅ 成功: {len(successful_accounts)} 个账号")
    print(f"❌ 失败: {len(failed_accounts)} 个账号")
    
    if successful_accounts:
        print("\n✅ 可用账号:")
        for email in successful_accounts:
            print(f"  - {email}")
    
    if failed_accounts:
        print("\n❌ 失败账号:")
        for email, error in failed_accounts:
            print(f"  - {email}: {error}")
    
    return len(successful_accounts) > 0

def test_email_generation():
    """测试邮箱生成功能"""
    print("\n🔄 测试邮箱生成功能...")
    
    try:
        # 导入2925邮箱生成器中的Original2925Provider
        sys.path.append('.')
        from 2925_email_generator import Original2925Provider
        
        provider = Original2925Provider()
        print("✅ Original2925Provider 创建成功")
        
        # 测试邮箱生成
        email, password, token, provider_extra = provider.generate_email()
        print(f"✅ 邮箱生成成功: {email}")
        print(f"账号信息: {provider_extra.get('account', {}).get('email', '未知')}")
        
        # 测试获取邮件
        print("🔄 测试获取邮件...")
        messages = provider.get_messages(email, token, provider_extra)
        print(f"✅ 获取邮件成功，找到 {len(messages)} 封邮件")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🎯 2925邮箱服务连接测试")
    print("="*60)
    
    # 测试POP3连接
    connection_ok = test_2925_connection()
    
    if connection_ok:
        # 测试邮箱生成
        generation_ok = test_email_generation()
        
        if generation_ok:
            print("\n🎉 所有测试通过！2925邮箱服务工作正常。")
        else:
            print("\n⚠️  POP3连接正常，但邮箱生成功能有问题。")
    else:
        print("\n❌ POP3连接失败，请检查网络连接和服务器状态。")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
