#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全仿照原始软件的订单验证界面
"""

import sys
import os
import json
import platform
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QFrame, QApplication,
                            QGraphicsDropShadowEffect, QSpacerItem, QSizePolicy,
                            QMessageBox, QProgressBar)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QColor, QPainter, QLinearGradient

class OrderVerifyThread(QThread):
    """订单验证线程"""
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(int)
    message = pyqtSignal(str)
    
    def __init__(self, order_number):
        super().__init__()
        self.order_number = order_number
    
    def run(self):
        """运行验证"""
        try:
            self.message.emit("正在验证订单格式...")
            self.progress.emit(25)
            
            # 验证订单号格式
            if not self.validate_order_format():
                self.finished.emit(False, "订单号格式不正确，必须是19位数字")
                return
            
            self.message.emit("正在检查本地记录...")
            self.progress.emit(50)
            
            # 检查本地黑名单
            if self.check_local_blacklist():
                self.finished.emit(False, "此订单号已被使用")
                return
            
            self.message.emit("正在验证订单有效性...")
            self.progress.emit(75)
            
            # 模拟验证过程
            import time
            time.sleep(1)
            
            self.message.emit("验证完成")
            self.progress.emit(100)
            
            # 保存订单
            self.save_order_to_blacklist()
            self.finished.emit(True, "订单验证成功！")
                
        except Exception as e:
            self.finished.emit(False, f"验证失败: {str(e)}")
    
    def validate_order_format(self):
        """验证订单号格式"""
        return len(self.order_number) == 19 and self.order_number.isdigit()
    
    def check_local_blacklist(self):
        """检查本地黑名单"""
        try:
            if platform.system() == "Darwin":
                blacklist_path = os.path.expanduser("~/blacklist1.json")
            else:
                blacklist_path = "C:/blacklist1.json"
            
            if os.path.exists(blacklist_path):
                with open(blacklist_path, 'r', encoding='utf-8') as f:
                    blacklist = json.load(f)
                    return self.order_number in blacklist
            return False
        except:
            return False
    
    def save_order_to_blacklist(self):
        """保存订单到黑名单"""
        try:
            if platform.system() == "Darwin":
                blacklist_path = os.path.expanduser("~/blacklist1.json")
            else:
                blacklist_path = "C:/blacklist1.json"
            
            blacklist = []
            if os.path.exists(blacklist_path):
                with open(blacklist_path, 'r', encoding='utf-8') as f:
                    blacklist = json.load(f)
            
            if self.order_number not in blacklist:
                blacklist.append(self.order_number)
                
                with open(blacklist_path, 'w', encoding='utf-8') as f:
                    json.dump(blacklist, f, indent=2)
        except:
            pass

class OriginalOrderDialog(QDialog):
    """完全仿照原始软件的订单验证对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.verify_thread = None
        self.init_ui()
        self.apply_style()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("订单验证")
        self.setFixedSize(400, 300)
        self.setModal(True)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("orderContainer")
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)
        
        # 内容布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 标题栏
        self.create_title_bar(layout)
        
        # 内容区域
        self.create_content_area(layout)
        
        # 添加阴影
        self.add_shadow()
    
    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_bar = QFrame()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(35)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 0, 15, 0)
        
        title_icon = QLabel("🔐")
        title_icon.setStyleSheet("font-size: 16px; color: white;")
        title_label = QLabel("订单验证")
        title_label.setObjectName("titleText")
        
        close_btn = QPushButton("✕")
        close_btn.setObjectName("closeButton")
        close_btn.setFixedSize(20, 20)
        close_btn.clicked.connect(self.close)
        
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(close_btn)
        
        parent_layout.addWidget(title_bar)
    
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        content_area = QFrame()
        content_area.setObjectName("contentArea")
        
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(25, 25, 25, 25)
        content_layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("订单验证")
        title_label.setObjectName("contentTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 紫色分割线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setObjectName("purpleLine")
        line.setFixedHeight(2)
        
        # 描述
        desc_label = QLabel("请输入19位数字订单号")
        desc_label.setObjectName("descText")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 订单号输入框
        self.order_input = QLineEdit()
        self.order_input.setPlaceholderText("输入19位订单号")
        self.order_input.setObjectName("orderInput")
        self.order_input.setMaxLength(19)
        self.order_input.textChanged.connect(self.on_order_changed)
        
        # 状态标签
        self.status_label = QLabel("等待输入订单号")
        self.status_label.setObjectName("statusText")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setVisible(False)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setObjectName("cancelButton")
        self.cancel_btn.setFixedHeight(40)
        self.cancel_btn.clicked.connect(self.reject)
        
        self.verify_btn = QPushButton("验证")
        self.verify_btn.setObjectName("verifyButton")
        self.verify_btn.setFixedHeight(40)
        self.verify_btn.setEnabled(False)
        self.verify_btn.clicked.connect(self.start_verification)
        
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.verify_btn)
        
        content_layout.addWidget(title_label)
        content_layout.addWidget(line)
        content_layout.addWidget(desc_label)
        content_layout.addWidget(self.order_input)
        content_layout.addWidget(self.status_label)
        content_layout.addWidget(self.progress_bar)
        content_layout.addLayout(button_layout)
        
        parent_layout.addWidget(content_area)
    
    def add_shadow(self):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 160))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
    
    def apply_style(self):
        """应用样式"""
        self.setStyleSheet("""
            #orderContainer {
                background: #1a1a1a;
                border: 1px solid #333;
                border-radius: 0px;
            }
            
            #titleBar {
                background: #2a2a2a;
                border-bottom: 1px solid #333;
            }
            
            #titleText {
                color: white;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #closeButton {
                background: #ff5f56;
                border: none;
                border-radius: 10px;
                color: white;
                font-size: 10px;
                font-weight: bold;
            }
            
            #closeButton:hover {
                background: #ff3b30;
            }
            
            #contentArea {
                background: #1a1a1a;
            }
            
            #contentTitle {
                color: white;
                font-size: 18px;
                font-weight: 600;
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 10px 0;
            }
            
            #purpleLine {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 #8b5cf6, stop:1 transparent);
                border: none;
                margin: 5px 0;
            }
            
            #descText {
                color: #ccc;
                font-size: 14px;
                font-weight: 400;
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 10px 0;
            }
            
            #orderInput {
                background: #2a2a2a;
                border: 2px solid #444;
                border-radius: 6px;
                color: white;
                font-size: 16px;
                font-family: 'Consolas', monospace;
                padding: 12px 15px;
                min-height: 20px;
            }
            
            #orderInput:focus {
                border-color: #8b5cf6;
            }
            
            #statusText {
                color: #888;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
                padding: 8px;
                background: #2a2a2a;
                border-radius: 6px;
                border: 1px solid #444;
            }
            
            #progressBar {
                background: #2a2a2a;
                border: 1px solid #444;
                border-radius: 6px;
                text-align: center;
                color: white;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #progressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #8b5cf6, stop:1 #a855f7);
                border-radius: 4px;
            }
            
            #verifyButton {
                background: transparent;
                border: 2px solid #8b5cf6;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #verifyButton:hover {
                background: rgba(139, 92, 246, 0.1);
            }
            
            #verifyButton:pressed {
                background: rgba(139, 92, 246, 0.2);
            }
            
            #verifyButton:disabled {
                border-color: #555;
                color: #666;
            }
            
            #cancelButton {
                background: transparent;
                border: 2px solid #666;
                border-radius: 8px;
                color: #ccc;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #cancelButton:hover {
                background: rgba(102, 102, 102, 0.1);
                border-color: #888;
                color: white;
            }
            
            #cancelButton:pressed {
                background: rgba(102, 102, 102, 0.2);
            }
        """)
    
    def on_order_changed(self, text):
        """订单号输入变化"""
        # 只允许数字
        filtered_text = ''.join(filter(str.isdigit, text))
        if filtered_text != text:
            self.order_input.setText(filtered_text)
            return
        
        # 检查长度并更新状态
        if len(filtered_text) == 19:
            self.verify_btn.setEnabled(True)
            self.status_label.setText("✅ 订单号格式正确，可以验证")
            self.status_label.setStyleSheet("""
                color: #00d4aa;
                font-size: 13px;
                font-weight: 500;
                padding: 8px;
                background: #2a2a2a;
                border-radius: 6px;
                border: 1px solid #00d4aa;
            """)
        elif len(filtered_text) > 0:
            self.verify_btn.setEnabled(False)
            self.status_label.setText(f"⏳ 订单号长度: {len(filtered_text)}/19")
            self.status_label.setStyleSheet("""
                color: #ffc107;
                font-size: 13px;
                font-weight: 500;
                padding: 8px;
                background: #2a2a2a;
                border-radius: 6px;
                border: 1px solid #ffc107;
            """)
        else:
            self.verify_btn.setEnabled(False)
            self.status_label.setText("等待输入订单号")
            self.status_label.setStyleSheet("""
                color: #888;
                font-size: 13px;
                font-weight: 500;
                padding: 8px;
                background: #2a2a2a;
                border-radius: 6px;
                border: 1px solid #444;
            """)
    
    def start_verification(self):
        """开始验证"""
        order_number = self.order_input.text().strip()
        
        if len(order_number) != 19 or not order_number.isdigit():
            self.show_error("请输入19位数字订单号")
            return
        
        # 禁用输入和按钮
        self.order_input.setEnabled(False)
        self.verify_btn.setEnabled(False)
        self.verify_btn.setText("验证中...")
        self.progress_bar.setVisible(True)
        
        # 启动验证线程
        self.verify_thread = OrderVerifyThread(order_number)
        self.verify_thread.finished.connect(self.on_verification_finished)
        self.verify_thread.progress.connect(self.progress_bar.setValue)
        self.verify_thread.message.connect(self.status_label.setText)
        self.verify_thread.start()
    
    def on_verification_finished(self, success, message):
        """验证完成"""
        self.progress_bar.setVisible(False)
        
        if success:
            self.status_label.setText(f"✅ {message}")
            self.status_label.setStyleSheet("""
                color: #00d4aa;
                font-size: 13px;
                font-weight: 500;
                padding: 8px;
                background: #2a2a2a;
                border-radius: 6px;
                border: 1px solid #00d4aa;
            """)
            
            # 延迟关闭对话框
            QTimer.singleShot(1500, self.accept)
        else:
            self.status_label.setText(f"❌ {message}")
            self.status_label.setStyleSheet("""
                color: #ff6b6b;
                font-size: 13px;
                font-weight: 500;
                padding: 8px;
                background: #2a2a2a;
                border-radius: 6px;
                border: 1px solid #ff6b6b;
            """)
            
            # 重新启用输入
            self.order_input.setEnabled(True)
            self.verify_btn.setEnabled(True)
            self.verify_btn.setText("重试")
    
    def show_error(self, message):
        """显示错误"""
        self.status_label.setText(f"❌ {message}")
        self.status_label.setStyleSheet("""
            color: #ff6b6b;
            font-size: 13px;
            font-weight: 500;
            padding: 8px;
            background: #2a2a2a;
            border-radius: 6px;
            border: 1px solid #ff6b6b;
        """)

def show_original_order_dialog(parent=None):
    """显示原始风格订单验证对话框"""
    dialog = OriginalOrderDialog(parent)
    return dialog.exec() == QDialog.DialogCode.Accepted

def main():
    """测试函数"""
    app = QApplication(sys.argv)
    
    # 设置字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    dialog = OriginalOrderDialog()
    
    # 居中显示
    screen = app.primaryScreen().geometry()
    dialog.move(
        (screen.width() - dialog.width()) // 2,
        (screen.height() - dialog.height()) // 2
    )
    
    result = dialog.exec()
    print(f"验证结果: {'成功' if result == QDialog.DialogCode.Accepted else '取消'}")

if __name__ == "__main__":
    main()
