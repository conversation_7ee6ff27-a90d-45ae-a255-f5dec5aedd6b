#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱验证码监控对话框
"""

import sys
import os
import re
import time
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTextEdit, QFrame, QApplication,
                            QGraphicsDropShadowEffect, QProgressBar)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QColor

class EmailMonitorThread(QThread):
    """邮箱监控线程"""
    code_received = pyqtSignal(str)
    message_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, email_address):
        super().__init__()
        self.email_address = email_address
        self.running = True
        self.check_count = 0
        self.max_checks = 40  # 最多检查40次（2分钟）
    
    def run(self):
        """运行监控"""
        try:
            import email_manager
            
            # 创建邮箱管理器
            manager = email_manager.EmailManager()
            
            self.message_updated.emit(f"开始监控邮箱: {self.email_address}")
            
            while self.running and self.check_count < self.max_checks:
                try:
                    self.check_count += 1
                    self.message_updated.emit(f"正在检查邮件... ({self.check_count}/{self.max_checks})")
                    
                    # 获取验证码
                    code = manager.get_verification_code(self.email_address)
                    
                    if code:
                        self.message_updated.emit(f"✅ 找到验证码: {code}")
                        self.code_received.emit(code)
                        return
                    
                    # 等待3秒后继续检查
                    time.sleep(3)
                    
                except Exception as e:
                    self.message_updated.emit(f"检查邮件时出错: {str(e)}")
                    time.sleep(3)
            
            if self.check_count >= self.max_checks:
                self.error_occurred.emit("监控超时，未收到验证码")
            
        except Exception as e:
            self.error_occurred.emit(f"监控失败: {str(e)}")
    
    def stop(self):
        """停止监控"""
        self.running = False

class EmailMonitorDialog(QDialog):
    """邮箱验证码监控对话框"""
    
    def __init__(self, email_address, parent=None):
        super().__init__(parent)
        self.email_address = email_address
        self.monitor_thread = None
        self.init_ui()
        self.apply_style()
        self.start_monitoring()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("验证码监控")
        self.setFixedSize(450, 350)
        self.setModal(True)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)
        
        # 主容器
        main_container = QFrame()
        main_container.setObjectName("monitorContainer")
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(main_container)
        
        # 内容布局
        layout = QVBoxLayout(main_container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 标题栏
        self.create_title_bar(layout)
        
        # 内容区域
        self.create_content_area(layout)
        
        # 添加阴影
        self.add_shadow()
    
    def create_title_bar(self, parent_layout):
        """创建标题栏"""
        title_bar = QFrame()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(35)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(15, 0, 15, 0)
        
        title_icon = QLabel("📧")
        title_icon.setStyleSheet("font-size: 16px; color: white;")
        title_label = QLabel("验证码监控")
        title_label.setObjectName("titleText")
        
        close_btn = QPushButton("✕")
        close_btn.setObjectName("closeButton")
        close_btn.setFixedSize(20, 20)
        close_btn.clicked.connect(self.close_monitoring)
        
        title_layout.addWidget(title_icon)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(close_btn)
        
        parent_layout.addWidget(title_bar)
    
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        content_area = QFrame()
        content_area.setObjectName("contentArea")
        
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(25, 25, 25, 25)
        content_layout.setSpacing(20)
        
        # 邮箱信息
        email_label = QLabel(f"监控邮箱: {self.email_address}")
        email_label.setObjectName("emailLabel")
        email_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 紫色分割线
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setObjectName("purpleLine")
        line.setFixedHeight(2)
        
        # 状态显示
        self.status_label = QLabel("准备开始监控...")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progressBar")
        self.progress_bar.setRange(0, 40)  # 最多40次检查
        self.progress_bar.setValue(0)
        
        # 日志显示
        self.log_text = QTextEdit()
        self.log_text.setObjectName("logText")
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        
        # 验证码显示
        self.code_label = QLabel("验证码: 等待中...")
        self.code_label.setObjectName("codeLabel")
        self.code_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        
        self.stop_btn = QPushButton("停止监控")
        self.stop_btn.setObjectName("stopButton")
        self.stop_btn.setFixedHeight(40)
        self.stop_btn.clicked.connect(self.close_monitoring)
        
        self.copy_btn = QPushButton("复制验证码")
        self.copy_btn.setObjectName("copyButton")
        self.copy_btn.setFixedHeight(40)
        self.copy_btn.setEnabled(False)
        self.copy_btn.clicked.connect(self.copy_code)
        
        button_layout.addWidget(self.stop_btn)
        button_layout.addWidget(self.copy_btn)
        
        content_layout.addWidget(email_label)
        content_layout.addWidget(line)
        content_layout.addWidget(self.status_label)
        content_layout.addWidget(self.progress_bar)
        content_layout.addWidget(self.log_text)
        content_layout.addWidget(self.code_label)
        content_layout.addLayout(button_layout)
        
        parent_layout.addWidget(content_area)
    
    def add_shadow(self):
        """添加阴影效果"""
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 160))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
    
    def apply_style(self):
        """应用样式"""
        self.setStyleSheet("""
            #monitorContainer {
                background: #1a1a1a;
                border: 1px solid #333;
                border-radius: 0px;
            }
            
            #titleBar {
                background: #2a2a2a;
                border-bottom: 1px solid #333;
            }
            
            #titleText {
                color: white;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #closeButton {
                background: #ff5f56;
                border: none;
                border-radius: 10px;
                color: white;
                font-size: 10px;
                font-weight: bold;
            }
            
            #closeButton:hover {
                background: #ff3b30;
            }
            
            #contentArea {
                background: #1a1a1a;
            }
            
            #emailLabel {
                color: #00d4aa;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
                background: #2a2a2a;
                border: 1px solid #00d4aa;
                border-radius: 6px;
                padding: 8px;
            }
            
            #purpleLine {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 transparent, stop:0.5 #8b5cf6, stop:1 transparent);
                border: none;
                margin: 5px 0;
            }
            
            #statusLabel {
                color: white;
                font-size: 13px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
                padding: 8px;
                background: #2a2a2a;
                border-radius: 6px;
                border: 1px solid #444;
            }
            
            #progressBar {
                background: #2a2a2a;
                border: 1px solid #444;
                border-radius: 6px;
                text-align: center;
                color: white;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #progressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #8b5cf6, stop:1 #a855f7);
                border-radius: 4px;
            }
            
            #logText {
                background: #2a2a2a;
                border: 1px solid #444;
                border-radius: 6px;
                color: #ccc;
                font-size: 12px;
                font-family: 'Consolas', monospace;
                padding: 8px;
            }
            
            #codeLabel {
                color: #ffc107;
                font-size: 16px;
                font-weight: 600;
                font-family: 'Consolas', monospace;
                background: #2a2a2a;
                border: 2px solid #ffc107;
                border-radius: 6px;
                padding: 12px;
            }
            
            #stopButton {
                background: transparent;
                border: 2px solid #ff6b6b;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #stopButton:hover {
                background: rgba(255, 107, 107, 0.1);
            }
            
            #copyButton {
                background: transparent;
                border: 2px solid #00d4aa;
                border-radius: 8px;
                color: white;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Microsoft YaHei', sans-serif;
            }
            
            #copyButton:hover {
                background: rgba(0, 212, 170, 0.1);
            }
            
            #copyButton:disabled {
                border-color: #555;
                color: #666;
            }
        """)
    
    def start_monitoring(self):
        """开始监控"""
        self.monitor_thread = EmailMonitorThread(self.email_address)
        self.monitor_thread.code_received.connect(self.on_code_received)
        self.monitor_thread.message_updated.connect(self.on_message_updated)
        self.monitor_thread.error_occurred.connect(self.on_error_occurred)
        self.monitor_thread.start()
        
        # 启动进度更新定时器
        self.progress_timer = QTimer()
        self.progress_timer.timeout.connect(self.update_progress)
        self.progress_timer.start(3000)  # 每3秒更新一次
    
    def update_progress(self):
        """更新进度"""
        if self.monitor_thread and self.monitor_thread.isRunning():
            self.progress_bar.setValue(self.monitor_thread.check_count)
    
    def on_code_received(self, code):
        """收到验证码"""
        self.current_code = code
        self.code_label.setText(f"验证码: {code}")
        self.code_label.setStyleSheet("""
            color: #00d4aa;
            font-size: 18px;
            font-weight: 700;
            font-family: 'Consolas', monospace;
            background: #2a2a2a;
            border: 2px solid #00d4aa;
            border-radius: 6px;
            padding: 12px;
        """)
        self.copy_btn.setEnabled(True)
        self.stop_btn.setText("关闭")
        
        # 停止进度定时器
        if hasattr(self, 'progress_timer'):
            self.progress_timer.stop()
        
        self.progress_bar.setValue(self.progress_bar.maximum())
    
    def on_message_updated(self, message):
        """更新消息"""
        self.status_label.setText(message)
        self.log_text.append(f"[{time.strftime('%H:%M:%S')}] {message}")
    
    def on_error_occurred(self, error):
        """发生错误"""
        self.status_label.setText(f"❌ {error}")
        self.status_label.setStyleSheet("""
            color: #ff6b6b;
            font-size: 13px;
            font-weight: 500;
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 8px;
            background: #2a2a2a;
            border-radius: 6px;
            border: 1px solid #ff6b6b;
        """)
        self.log_text.append(f"[{time.strftime('%H:%M:%S')}] ❌ {error}")
        self.stop_btn.setText("关闭")
        
        # 停止进度定时器
        if hasattr(self, 'progress_timer'):
            self.progress_timer.stop()
    
    def copy_code(self):
        """复制验证码"""
        if hasattr(self, 'current_code'):
            clipboard = QApplication.clipboard()
            clipboard.setText(self.current_code)
            self.log_text.append(f"[{time.strftime('%H:%M:%S')}] ✅ 验证码已复制到剪贴板")
    
    def close_monitoring(self):
        """关闭监控"""
        if self.monitor_thread and self.monitor_thread.isRunning():
            self.monitor_thread.stop()
            self.monitor_thread.wait(1000)  # 等待1秒
        
        if hasattr(self, 'progress_timer'):
            self.progress_timer.stop()
        
        self.close()

def show_email_monitor_dialog(email_address, parent=None):
    """显示邮箱监控对话框"""
    dialog = EmailMonitorDialog(email_address, parent)
    dialog.exec()

def main():
    """测试函数"""
    app = QApplication(sys.argv)
    
    # 设置字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    # 测试邮箱地址
    test_email = "<EMAIL>"
    
    dialog = EmailMonitorDialog(test_email)
    
    # 居中显示
    screen = app.primaryScreen().geometry()
    dialog.move(
        (screen.width() - dialog.width()) // 2,
        (screen.height() - dialog.height()) // 2
    )
    
    dialog.exec()

if __name__ == "__main__":
    main()
