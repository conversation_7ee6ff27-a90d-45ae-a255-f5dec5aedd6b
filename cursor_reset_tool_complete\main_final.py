#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor Pro 无限续杯工具 - 完整版主启动器
自动检测环境并选择最佳的运行模式（GUI或控制台）
"""

import os
import sys
import platform
from colorama import Fore, Style, init

# 初始化colorama
init()

def print_banner():
    """打印程序横幅"""
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")
    print(f"{Fore.CYAN}🎯 Cursor Pro 无限续杯工具 v2.2.3{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Pro Version Activator - 完整版{Style.RESET_ALL}")
    print(f"{Fore.CYAN}Author: Pin Studios (yeongpin){Style.RESET_ALL}")
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}")

def check_gui_support():
    """检查GUI支持"""
    try:
        # 检查PyQt6
        import PyQt6
        from PyQt6.QtWidgets import QApplication
        
        # 检查显示环境
        if platform.system() == "Windows":
            return True
        elif platform.system() == "Darwin":  # macOS
            return True
        else:  # Linux
            return os.environ.get('DISPLAY') is not None
            
    except ImportError:
        return False
    except Exception:
        return False

def check_dependencies():
    """检查依赖包"""
    required_packages = {
        'PyQt6': 'PyQt6图形界面库',
        'requests': 'HTTP请求库',
        'colorama': '终端颜色库',
        'psutil': '进程管理库'
    }
    
    missing = []
    available = []
    
    for package, description in required_packages.items():
        try:
            __import__(package)
            available.append((package, description))
        except ImportError:
            missing.append((package, description))
    
    return available, missing

def show_dependency_info(available, missing):
    """显示依赖信息"""
    print(f"\n{Fore.CYAN}📦 依赖包检查:{Style.RESET_ALL}")
    
    if available:
        print(f"{Fore.GREEN}✅ 已安装的包:{Style.RESET_ALL}")
        for package, desc in available:
            print(f"  • {package} - {desc}")
    
    if missing:
        print(f"{Fore.YELLOW}⚠️  缺少的包:{Style.RESET_ALL}")
        for package, desc in missing:
            print(f"  • {package} - {desc}")
        
        print(f"\n{Fore.YELLOW}安装命令:{Style.RESET_ALL}")
        packages = [pkg for pkg, _ in missing]
        print(f"  pip install {' '.join(packages)}")

def get_user_choice():
    """获取用户选择"""
    print(f"\n{Fore.CYAN}🚀 请选择运行模式:{Style.RESET_ALL}")
    print(f"{Fore.GREEN}1{Style.RESET_ALL}. 🌙 深色主题界面（推荐）")
    print(f"{Fore.GREEN}2{Style.RESET_ALL}. 🎨 现代化GUI界面")
    print(f"{Fore.GREEN}3{Style.RESET_ALL}. 🖥️  完整GUI图形界面")
    print(f"{Fore.GREEN}4{Style.RESET_ALL}. 💻 控制台命令行模式")
    print(f"{Fore.GREEN}5{Style.RESET_ALL}. 🧪 GUI测试模式（简化版）")
    print(f"{Fore.GREEN}0{Style.RESET_ALL}. ❌ 退出程序")
    
    while True:
        try:
            choice = input(f"\n{Fore.CYAN}请输入选择 (0-4): {Style.RESET_ALL}").strip()
            if choice in ['0', '1', '2', '3', '4']:
                return choice
            else:
                print(f"{Fore.RED}❌ 无效选择，请输入0-4{Style.RESET_ALL}")
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}程序已取消{Style.RESET_ALL}")
            return '0'

def run_gui_mode():
    """运行GUI模式"""
    try:
        print(f"{Fore.CYAN}🚀 正在启动GUI模式...{Style.RESET_ALL}")
        from main_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"{Fore.RED}❌ GUI模块导入失败: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🔄 自动切换到控制台模式...{Style.RESET_ALL}")
        run_console_mode()
    except Exception as e:
        print(f"{Fore.RED}❌ GUI模式启动失败: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🔄 自动切换到控制台模式...{Style.RESET_ALL}")
        run_console_mode()

def run_console_mode():
    """运行控制台模式"""
    try:
        print(f"{Fore.CYAN}🚀 正在启动控制台模式...{Style.RESET_ALL}")
        from main_console import main as console_main
        console_main()
    except Exception as e:
        print(f"{Fore.RED}❌ 控制台模式启动失败: {e}{Style.RESET_ALL}")
        input("按回车键退出...")

def run_modern_gui_mode():
    """运行现代化GUI模式"""
    try:
        print(f"{Fore.CYAN}🚀 正在启动现代化GUI界面...{Style.RESET_ALL}")
        from ui_modern import main as modern_gui_main
        modern_gui_main()
    except ImportError as e:
        print(f"{Fore.RED}❌ 现代化GUI模块导入失败: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🔄 自动切换到控制台模式...{Style.RESET_ALL}")
        run_console_mode()
    except Exception as e:
        print(f"{Fore.RED}❌ 现代化GUI模式启动失败: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🔄 自动切换到控制台模式...{Style.RESET_ALL}")
        run_console_mode()

def run_gui_test_mode():
    """运行GUI测试模式"""
    try:
        print(f"{Fore.CYAN}🚀 正在启动GUI测试模式...{Style.RESET_ALL}")
        from test_gui import main as test_gui_main
        test_gui_main()
    except ImportError as e:
        print(f"{Fore.RED}❌ GUI测试模块导入失败: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🔄 自动切换到控制台模式...{Style.RESET_ALL}")
        run_console_mode()
    except Exception as e:
        print(f"{Fore.RED}❌ GUI测试模式启动失败: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🔄 自动切换到控制台模式...{Style.RESET_ALL}")
        run_console_mode()

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    available, missing = check_dependencies()
    show_dependency_info(available, missing)
    
    # 检查GUI支持
    gui_supported = check_gui_support()
    
    print(f"\n{Fore.CYAN}🔍 环境检查:{Style.RESET_ALL}")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  Python版本: {sys.version.split()[0]}")
    print(f"  GUI支持: {'✅ 支持' if gui_supported else '❌ 不支持'}")
    
    # 如果没有GUI支持且缺少PyQt6，直接运行控制台模式
    if not gui_supported and any(pkg == 'PyQt6' for pkg, _ in missing):
        print(f"\n{Fore.YELLOW}⚠️  检测到无GUI环境，自动启动控制台模式{Style.RESET_ALL}")
        run_console_mode()
        return
    
    # 获取用户选择
    choice = get_user_choice()
    
    if choice == '0':
        print(f"\n{Fore.YELLOW}👋 再见！{Style.RESET_ALL}")
        return
    elif choice == '1':
        if gui_supported:
            run_modern_gui_mode()
        else:
            print(f"{Fore.RED}❌ GUI环境不支持，自动切换到控制台模式{Style.RESET_ALL}")
            run_console_mode()
    elif choice == '2':
        if gui_supported:
            run_gui_mode()
        else:
            print(f"{Fore.RED}❌ GUI环境不支持，自动切换到控制台模式{Style.RESET_ALL}")
            run_console_mode()
    elif choice == '3':
        run_console_mode()
    elif choice == '4':
        if gui_supported:
            run_gui_test_mode()
        else:
            print(f"{Fore.RED}❌ GUI环境不支持，自动切换到控制台模式{Style.RESET_ALL}")
            run_console_mode()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}程序已终止{Style.RESET_ALL}")
    except Exception as e:
        print(f"\n{Fore.RED}程序异常: {e}{Style.RESET_ALL}")
        input("按回车键退出...")
