import tkinter as tk
import customtkinter as ctk
import pyperclip
import random
import string
import json
import os
import threading
import time
import re
import requests
from datetime import datetime
from tkinter import messagebox
from bs4 import BeautifulSoup
import psutil
import platform
import shutil
import sqlite3
import uuid
import hashlib
import tempfile
import glob
import configparser
import traceback
import sys
import socket
import poplib

# ========== Cursor重置功能模块 ==========
class CursorResetManager:
    """Cursor重置管理器"""

    def __init__(self):
        self.emoji = {
            "PROCESS": "⚙️",
            "SUCCESS": "✅",
            "ERROR": "❌",
            "INFO": "ℹ️",
            "WAIT": "⏳",
            "RESET": "🔄",
            "WARNING": "⚠️",
            "FILE": "📄",
            "BACKUP": "💾"
        }

    def quit_cursor_processes(self):
        """关闭Cursor进程"""
        try:
            print(f"{self.emoji['PROCESS']} 正在关闭Cursor进程...")
            cursor_processes = []

            # 收集所有Cursor进程
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'].lower() in ['cursor.exe', 'cursor']:
                        cursor_processes.append(proc)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if not cursor_processes:
                print(f"{self.emoji['INFO']} 未发现运行中的Cursor进程")
                return True

            # 温和地请求进程终止
            for proc in cursor_processes:
                try:
                    if proc.is_running():
                        print(f"{self.emoji['PROCESS']} 正在终止Cursor进程 (PID: {proc.pid})...")
                        proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # 等待进程关闭
            print(f"{self.emoji['WAIT']} 等待Cursor进程关闭...")
            time.sleep(3)

            # 检查是否还有进程在运行
            remaining_processes = []
            for proc in cursor_processes:
                try:
                    if proc.is_running():
                        remaining_processes.append(proc)
                except psutil.NoSuchProcess:
                    continue

            # 强制终止剩余进程
            if remaining_processes:
                for proc in remaining_processes:
                    try:
                        if proc.is_running():
                            print(f"{self.emoji['WARNING']} 强制终止Cursor进程 (PID: {proc.pid})...")
                            proc.kill()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                time.sleep(1)

            print(f"{self.emoji['SUCCESS']} 所有Cursor进程已成功关闭")
            return True

        except Exception as e:
            print(f"{self.emoji['ERROR']} 关闭Cursor进程时发生错误: {str(e)}")
            return False

    def get_state_db_path(self):
        """根据操作系统获取Cursor状态文件路径"""
        system = platform.system()

        if system == "Darwin":  # macOS
            return os.path.abspath(os.path.expanduser("~/Library/Application Support/Cursor/User/globalStorage/state.vscdb"))
        elif system == "Windows":  # Windows
            return os.path.abspath(os.path.expanduser("~/AppData/Roaming/Cursor/User/globalStorage/state.vscdb"))
        elif system == "Linux":  # Linux
            return os.path.abspath(os.path.expanduser("~/.config/Cursor/User/globalStorage/state.vscdb"))
        else:
            print(f"{self.emoji['ERROR']} 不支持的操作系统: {system}")
            return None

    def delete_state_db(self):
        """删除Cursor的state.vscdb文件"""
        try:
            # 先关闭Cursor进程
            print(f"{self.emoji['RESET']} 开始重置Cursor状态...")
            if not self.quit_cursor_processes():
                print(f"{self.emoji['WARNING']} 无法完全关闭Cursor进程，继续执行...")

            # 获取状态文件路径
            state_db_path = self.get_state_db_path()
            if not state_db_path:
                return False

            print(f"{self.emoji['FILE']} 状态文件路径: {state_db_path}")

            # 检查文件是否存在
            if os.path.exists(state_db_path):
                # 创建备份
                backup_path = f"{state_db_path}.backup_{int(time.time())}"
                try:
                    shutil.copy2(state_db_path, backup_path)
                    print(f"{self.emoji['BACKUP']} 已创建备份: {backup_path}")
                except Exception as e:
                    print(f"{self.emoji['WARNING']} 创建备份失败: {str(e)}")

                # 删除状态文件
                try:
                    os.remove(state_db_path)
                    print(f"{self.emoji['SUCCESS']} 状态文件已删除")
                    return True
                except Exception as e:
                    print(f"{self.emoji['ERROR']} 删除状态文件失败: {str(e)}")
                    return False
            else:
                print(f"{self.emoji['INFO']} 状态文件不存在，无需删除")
                return True

        except Exception as e:
            print(f"{self.emoji['ERROR']} 重置Cursor状态时发生错误: {str(e)}")
            return False

    def reset_machine_id(self):
        """重置机器ID"""
        try:
            print(f"{self.emoji['RESET']} 开始重置机器ID...")

            # 先关闭Cursor进程
            if not self.quit_cursor_processes():
                print(f"{self.emoji['WARNING']} 无法完全关闭Cursor进程，继续执行...")

            # 删除状态数据库
            if not self.delete_state_db():
                print(f"{self.emoji['WARNING']} 删除状态数据库失败，继续执行...")

            # 生成新的机器ID
            new_machine_id = str(uuid.uuid4())
            print(f"{self.emoji['INFO']} 新机器ID: {new_machine_id}")

            # 这里可以添加更多的重置逻辑，比如清理其他配置文件

            print(f"{self.emoji['SUCCESS']} 机器ID重置完成")
            return True

        except Exception as e:
            print(f"{self.emoji['ERROR']} 重置机器ID时发生错误: {str(e)}")
            return False

# ========== 邮箱服务提供者抽象层 ==========
class EmailProviderBase:
    def get_name(self):
        raise NotImplementedError
    def generate_email(self):
        """返回(email, password, token, provider_extra)"""
        raise NotImplementedError
    def get_messages(self, email, token, provider_extra):
        raise NotImplementedError
    def get_message_detail(self, message_id, email, token, provider_extra):
        raise NotImplementedError
    def delete_message(self, message_id, email, token, provider_extra):
        raise NotImplementedError

# mail.tm 实现
class MailTmProvider(EmailProviderBase):
    def __init__(self, get_proxies_func=None):
        self.get_proxies_func = get_proxies_func
    def get_name(self):
        return "mail.tm"
    def generate_email(self):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        proxies = self.get_proxies_func() if self.get_proxies_func else None
        domains_response = requests.get("https://api.mail.tm/domains", headers=headers, proxies=proxies)
        domains_data = domains_response.json()
        if not isinstance(domains_data, list) or not domains_data:
            raise Exception("没有可用的域名")
        domain = domains_data[0]["domain"]
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@{domain}"
        password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))
        register_data = {"address": email, "password": password}
        register_response = requests.post(
            "https://api.mail.tm/accounts",
            json=register_data,
            headers=headers,
            proxies=proxies
        )
        if not register_response.ok:
            raise Exception(f"注册账号失败: {register_response.status_code}")
        login_data = {"address": email, "password": password}
        login_response = requests.post(
            "https://api.mail.tm/token",
            json=login_data,
            headers=headers,
            proxies=proxies
        )
        if not login_response.ok:
            raise Exception(f"登录失败: {login_response.status_code}")
        token_data = login_response.json()
        token = token_data.get("token")
        if not token:
            raise Exception("获取token失败")
        return email, password, token, {}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        proxies = self.get_proxies_func() if self.get_proxies_func else None
        response = requests.get("https://api.mail.tm/messages", headers=headers, proxies=proxies)
        if response.ok:
            data = response.json()
            if isinstance(data, list):
                return data
            else:
                return data.get("hydra:member", [])
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        proxies = self.get_proxies_func() if self.get_proxies_func else None
        response = requests.get(f"https://api.mail.tm/messages/{message_id}", headers=headers, proxies=proxies)
        if response.ok:
            return response.json()
        return None
    def delete_message(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        proxies = self.get_proxies_func() if self.get_proxies_func else None
        requests.delete(f"https://api.mail.tm/messages/{message_id}", headers=headers, proxies=proxies)

# Mail.tm 备用实现（使用不同域名）
class MailTmAltProvider(EmailProviderBase):
    def __init__(self, get_proxies_func=None):
        self.get_proxies_func = get_proxies_func
    def get_name(self):
        return "mail.tm (备用)"
    def generate_email(self):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        proxies = self.get_proxies_func() if self.get_proxies_func else None
        domains_response = requests.get("https://api.mail.tm/domains", headers=headers, proxies=proxies)
        domains_data = domains_response.json()
        if not isinstance(domains_data, list) or not domains_data:
            raise Exception("没有可用的域名")
        # 使用第二个域名（如果有的话）
        domain = domains_data[1]["domain"] if len(domains_data) > 1 else domains_data[0]["domain"]
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))  # 更长的用户名
        email = f"{username}@{domain}"
        password = ''.join(random.choices(string.ascii_letters + string.digits, k=14))  # 更长的密码
        register_data = {"address": email, "password": password}
        register_response = requests.post(
            "https://api.mail.tm/accounts",
            json=register_data,
            headers=headers,
            proxies=proxies
        )
        if not register_response.ok:
            raise Exception(f"注册账号失败: {register_response.status_code}")
        login_data = {"address": email, "password": password}
        login_response = requests.post(
            "https://api.mail.tm/token",
            json=login_data,
            headers=headers,
            proxies=proxies
        )
        if not login_response.ok:
            raise Exception(f"登录失败: {login_response.status_code}")
        token_data = login_response.json()
        token = token_data.get("token")
        if not token:
            raise Exception("获取token失败")
        return email, password, token, {}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        proxies = self.get_proxies_func() if self.get_proxies_func else None
        response = requests.get("https://api.mail.tm/messages", headers=headers, proxies=proxies)
        if response.ok:
            data = response.json()
            if isinstance(data, list):
                return data
            else:
                return data.get("hydra:member", [])
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        proxies = self.get_proxies_func() if self.get_proxies_func else None
        response = requests.get(f"https://api.mail.tm/messages/{message_id}", headers=headers, proxies=proxies)
        if response.ok:
            return response.json()
        return None
    def delete_message(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json",
            "Authorization": f"Bearer {token}"
        }
        proxies = self.get_proxies_func() if self.get_proxies_func else None
        requests.delete(f"https://api.mail.tm/messages/{message_id}", headers=headers, proxies=proxies)




        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            # 获取邮箱地址
            resp = requests.get("https://10minutemail.net/address.api.php", headers=headers, timeout=15)
            if resp.ok:
                data = resp.json()
                email = data.get("mail_get_mail")
                if email:
                    return email, None, None, {"email": email}
        except Exception as e:
            print(f"10MinuteMail.net 生成邮箱失败: {str(e)}")
        raise Exception("无法生成10MinuteMail.net邮箱")

    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            # 获取邮件列表
            resp = requests.get("https://10minutemail.net/mail.api.php", headers=headers, timeout=15)
            if resp.ok:
                data = resp.json()
                messages = data.get("mail_list", [])
                print(f"10MinuteMail.net: 获取到 {len(messages)} 封邮件")
                return messages
        except Exception as e:
            print(f"10MinuteMail.net 获取邮件失败: {str(e)}")
        return []

    def get_message_detail(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            # 获取邮件详情
            resp = requests.get(f"https://10minutemail.net/mail.api.php?id={message_id}", headers=headers, timeout=15)
            if resp.ok:
                return resp.json()
        except Exception as e:
            print(f"10MinuteMail.net 获取邮件详情失败: {str(e)}")
        return None

    def delete_message(self, message_id, email, token, provider_extra):
        # 10MinuteMail.net 不支持删除邮件
        pass

# TempMail.org 实现
class TempMailOrgProvider(EmailProviderBase):
    def get_name(self):
        return "tempmail.org"
    def generate_email(self):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json"
        }
        # 获取可用域名
        domains_response = requests.get("https://api.tempmail.org/request/domains/format/json/", headers=headers)
        if not domains_response.ok:
            raise Exception("无法获取域名列表")
        domains = domains_response.json()
        if not domains:
            raise Exception("没有可用的域名")
        domain = random.choice(domains)
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@{domain}"
        return email, None, None, {"username": username, "domain": domain}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json"
        }
        url = f"https://api.tempmail.org/request/mail/id/{email}/format/json/"
        resp = requests.get(url, headers=headers)
        if resp.ok:
            return resp.json()
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        # tempmail.org 在获取邮件列表时已包含详细信息
        return {"id": message_id, "subject": "邮件详情", "text": "请查看邮件列表"}
    def delete_message(self, message_id, email, token, provider_extra):
        # tempmail.org 不支持删除邮件
        pass





# Mohmal 实现
class MohmalProvider(EmailProviderBase):
    def get_name(self):
        return "mohmal"
    def generate_email(self):
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        domains = ["mohmal.com", "mohmal.tech", "mohmal.in"]
        domain = random.choice(domains)
        email = f"{username}@{domain}"
        return email, None, None, {"username": username, "domain": domain}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json"
        }
        username = provider_extra["username"]
        domain = provider_extra["domain"]
        url = f"https://www.mohmal.com/api/check/{username}@{domain}"
        resp = requests.get(url, headers=headers)
        if resp.ok:
            return resp.json()
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        # mohmal 在获取邮件列表时已包含详细信息
        return {"id": message_id, "subject": "邮件详情", "text": "请查看邮件列表"}
    def delete_message(self, message_id, email, token, provider_extra):
        pass



# MailDrop 实现
class MailDropProvider(EmailProviderBase):
    def get_name(self):
        return "maildrop"
    def generate_email(self):
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@maildrop.cc"
        return email, None, None, {"username": username}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json"
        }
        username = provider_extra["username"]
        url = f"https://maildrop.cc/api/inbox/{username}"
        try:
            resp = requests.get(url, headers=headers, timeout=10)
            if resp.ok:
                return resp.json()
        except:
            pass
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Accept": "application/json"
        }
        username = provider_extra["username"]
        url = f"https://maildrop.cc/api/inbox/{username}/{message_id}"
        try:
            resp = requests.get(url, headers=headers, timeout=10)
            if resp.ok:
                return resp.json()
        except:
            pass
        return None
    def delete_message(self, message_id, email, token, provider_extra):
        pass



# GetNada 实现
class GetNadaProvider(EmailProviderBase):
    def get_name(self):
        return "getnada"
    def generate_email(self):
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@getnada.com"
        return email, None, None, {"username": username}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            username = provider_extra["username"]
            url = f"https://getnada.com/api/v1/inboxes/{username}"
            resp = requests.get(url, headers=headers, timeout=10)
            if resp.ok:
                data = resp.json()
                emails = data.get("msgs", [])
                print(f"GetNada: 获取到 {len(emails)} 封邮件")
                return emails
        except Exception as e:
            print(f"GetNada 获取邮件失败: {str(e)}")
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            username = provider_extra["username"]
            url = f"https://getnada.com/api/v1/inboxes/{username}/{message_id}"
            resp = requests.get(url, headers=headers, timeout=10)
            if resp.ok:
                return resp.json()
        except Exception as e:
            print(f"GetNada 获取邮件详情失败: {str(e)}")
        return None
    def delete_message(self, message_id, email, token, provider_extra):
        pass

# Mailsac 实现
class MailsacProvider(EmailProviderBase):
    def get_name(self):
        return "mailsac"
    def generate_email(self):
        # Mailsac 允许使用任意邮箱地址
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@mailsac.com"
        return email, None, None, {"username": username}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            resp = requests.get(f"https://mailsac.com/api/addresses/{email}/messages",
                              headers=headers, timeout=15)
            if resp.ok:
                messages = resp.json()
                print(f"Mailsac: 获取到 {len(messages)} 封邮件")
                return messages
            elif resp.status_code == 404:
                print("Mailsac: 邮箱为空")
                return []
        except Exception as e:
            print(f"Mailsac 获取邮件失败: {str(e)}")
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            resp = requests.get(f"https://mailsac.com/api/addresses/{email}/messages/{message_id}",
                              headers=headers, timeout=15)
            if resp.ok:
                return resp.json()
        except Exception as e:
            print(f"Mailsac 获取邮件详情失败: {str(e)}")
        return None
    def delete_message(self, message_id, email, token, provider_extra):
        pass



# Mailinator 实现（简化版）
class MailinatorProvider(EmailProviderBase):
    def get_name(self):
        return "mailinator"
    def generate_email(self):
        # Mailinator 允许使用任意用户名
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        email = f"{username}@mailinator.com"
        return email, None, None, {"username": username}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            username = provider_extra["username"]
            # Mailinator 有公共API，但可能需要token
            resp = requests.get(f"https://www.mailinator.com/api/v2/domains/mailinator.com/inboxes/{username}",
                              headers=headers, timeout=15)
            if resp.ok:
                data = resp.json()
                messages = data.get("msgs", [])
                print(f"Mailinator: 获取到 {len(messages)} 封邮件")
                return messages
        except Exception as e:
            print(f"Mailinator 获取邮件失败: {str(e)}")
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        return None
    def delete_message(self, message_id, email, token, provider_extra):
        pass







# EmailOnDeck 实现
class EmailOnDeckProvider(EmailProviderBase):
    def get_name(self):
        return "emailondeck"
    def generate_email(self):
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
        email = f"{username}@emailondeck.com"
        return email, None, None, {"username": username}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            username = provider_extra["username"]
            url = f"https://www.emailondeck.com/f/gm.php?v={username}"
            resp = requests.get(url, headers=headers, timeout=10)
            if resp.ok:
                return resp.json() if resp.content else []
        except Exception as e:
            print(f"EmailOnDeck 获取邮件失败: {str(e)}")
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        return None
    def delete_message(self, message_id, email, token, provider_extra):
        pass

# InboxKitten 实现
class InboxKittenProvider(EmailProviderBase):
    def get_name(self):
        return "inboxkitten"
    def generate_email(self):
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
        email = f"{username}@inboxkitten.com"
        return email, None, None, {"username": username}
    def get_messages(self, email, token, provider_extra):
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "application/json"
        }
        try:
            username = provider_extra["username"]
            url = f"https://inboxkitten.com/api/v1/mail/{username}"
            resp = requests.get(url, headers=headers, timeout=10)
            if resp.ok:
                data = resp.json()
                messages = data.get("mails", [])
                print(f"InboxKitten: 获取到 {len(messages)} 封邮件")
                return messages
        except Exception as e:
            print(f"InboxKitten 获取邮件失败: {str(e)}")
        return []
    def get_message_detail(self, message_id, email, token, provider_extra):
        return None
    def delete_message(self, message_id, email, token, provider_extra):
        pass

# 2925.com 原软件邮箱服务实现
class Original2925Provider(EmailProviderBase):
    def __init__(self):
        # POP3服务器配置
        self.POP3_SERVER = 'pop.2925.com'
        self.POP3_PORT = 995
        self.POP3_SSL = True

        # 邮箱账号配置
        self.EMAIL_ACCOUNTS = [
            {'email': '<EMAIL>', 'password': 'mnysb123.'},
            {'email': '<EMAIL>', 'password': 'mnysb123.'},
            {'email': '<EMAIL>', 'password': 'mnysb123.'},
            {'email': '<EMAIL>', 'password': 'mnysb123.'},
            {'email': '<EMAIL>', 'password': 'mnysb666.'}
        ]

        # 加载domain.json文件
        self.domain_map = self.load_domain_map()

    def get_name(self):
        return ".icu"

    def load_domain_map(self):
        """从domain.json加载邮箱与域名的映射关系"""
        import platform
        # 根据系统类型设置路径
        domain_path = os.path.join(os.path.expanduser("~") if platform.system() == "Darwin" else "C:\\", "domain.json")

        # 尝试加载domain.json文件
        try:
            if os.path.exists(domain_path):
                with open(domain_path, 'r', encoding='utf-8') as f:
                    domain_map = json.load(f)
                print(f"成功从{domain_path}加载域名映射")
                return domain_map
            else:
                print(f"未找到domain.json文件: {domain_path}，将使用默认配置")
                return None
        except Exception as e:
            print(f"加载domain.json出错: {e}")
            return None

    def generate_email(self):
        """生成2925子邮箱地址"""
        if self.domain_map:
            # 从domain.json中随机选择一个邮箱
            email_address = random.choice(list(self.domain_map.keys()))

            # 获取该邮箱对应的域名列表
            domains = self.domain_map.get(email_address, [])

            if domains:
                # 随机选择一个域名
                domain = random.choice(domains)

                # 查找对应的账号和密码
                account = None
                for acc in self.EMAIL_ACCOUNTS:
                    if acc['email'] == email_address:
                        account = acc
                        break

                if not account:
                    print(f"在EMAIL_ACCOUNTS中未找到匹配的邮箱账号: {email_address}")
                    # 使用旧的方法作为备选
                    return self._generate_sub_email_old()

                # 生成6个随机字符（字母和数字）
                random_chars = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))

                # 创建子邮箱（域名@前随机添加字符）
                sub_email = f"{random_chars}@{domain}"

                # 记录使用的账号和域名信息
                print(f"随机选择邮箱账号: {email_address}, 域名: {domain}")

                return sub_email, None, None, {"account": account, "domain": domain, "sub_email": sub_email}
            else:
                print(f"选择的邮箱 {email_address} 没有对应的域名")
                return self._generate_sub_email_old()
        else:
            # 如果没有找到domain.json或加载失败，使用旧方法
            return self._generate_sub_email_old()

    def _generate_sub_email_old(self):
        """旧的子邮箱生成方法（备选方案）"""
        # 随机选择一个邮箱账号
        account = random.choice(self.EMAIL_ACCOUNTS)
        email_parts = account['email'].split('@')

        # 生成6个随机字符（字母和数字）
        random_chars = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))

        # 创建子邮箱（不使用+号，直接将随机字符添加到用户名后面）
        sub_email = f"{email_parts[0]}{random_chars}@{email_parts[1]}"

        # 记录使用的账号信息
        print(f"使用旧方法选择邮箱账号: {account['email']}")

        return sub_email, None, None, {"account": account, "sub_email": sub_email}

    def get_messages(self, email, token, provider_extra):
        """获取2925邮箱的邮件"""
        try:
            import poplib
            import email as email_lib
            from email.header import decode_header
            from datetime import datetime, timedelta, timezone
            import socket

            account = provider_extra.get("account")
            sub_email = provider_extra.get("sub_email", email)

            if not account:
                print("❌ 2925邮箱: 缺少账号信息")
                return []

            print(f"🔄 2925邮箱: 正在连接到 {self.POP3_SERVER}:{self.POP3_PORT}")
            print(f"🔄 2925邮箱: 使用账号 {account['email']}")
            print(f"🔄 2925邮箱: 目标子邮箱 {sub_email}")

            # 设置连接超时
            socket.setdefaulttimeout(10)

            # 连接到POP3服务器
            if self.POP3_SSL:
                mail_server = poplib.POP3_SSL(self.POP3_SERVER, self.POP3_PORT)
            else:
                mail_server = poplib.POP3(self.POP3_SERVER, self.POP3_PORT)

            print("✅ 2925邮箱: POP3连接成功")

            # 登录
            mail_server.user(account['email'])
            mail_server.pass_(account['password'])

            print("✅ 2925邮箱: 登录成功")

            # 获取邮件数量
            email_count = len(mail_server.list()[1])
            print(f"📧 2925邮箱: 共有 {email_count} 封邮件")

            messages = []

            # 检查最新的10封邮件
            start_index = max(1, email_count - 9)

            for i in range(email_count, start_index - 1, -1):
                try:
                    # 获取邮件
                    response, lines, octets = mail_server.retr(i)

                    # 解析邮件内容
                    msg_content = b'\r\n'.join(lines).decode('utf-8', errors='ignore')
                    msg = email_lib.message_from_string(msg_content)

                    # 获取收件人
                    to_field = msg.get('To', '')
                    print(f"📧 邮件#{i}: 收件人={to_field}, 目标={sub_email}")

                    # 检查收件人是否匹配（更宽松的匹配逻辑）
                    if sub_email.lower() not in to_field.lower():
                        # 尝试匹配域名部分（对于domain.json的域名映射）
                        sub_domain = sub_email.split('@')[1] if '@' in sub_email else ''
                        if sub_domain and sub_domain.lower() not in to_field.lower():
                            print(f"⏭️  邮件#{i}: 收件人不匹配，跳过")
                            continue
                        else:
                            print(f"✅ 邮件#{i}: 域名匹配，继续处理")
                    else:
                        print(f"✅ 邮件#{i}: 完全匹配，继续处理")

                    # 获取邮件主题
                    subject = msg.get('Subject', '')
                    if subject:
                        subject, encoding = decode_header(subject)[0]
                        if isinstance(subject, bytes):
                            subject = subject.decode(encoding or 'utf-8', errors='ignore')

                    # 获取发件人
                    from_field = msg.get('From', '')

                    # 获取邮件正文
                    body = ""
                    if msg.is_multipart():
                        for part in msg.walk():
                            content_type = part.get_content_type()
                            if content_type == "text/plain" or content_type == "text/html":
                                try:
                                    payload = part.get_payload(decode=True)
                                    charset = part.get_content_charset() or 'utf-8'
                                    body += payload.decode(charset, errors='ignore')
                                except Exception as e:
                                    print(f"解析邮件内容出错: {e}")
                    else:
                        try:
                            payload = msg.get_payload(decode=True)
                            charset = msg.get_content_charset() or 'utf-8'
                            body = payload.decode(charset, errors='ignore')
                        except Exception as e:
                            print(f"解析邮件内容出错: {e}")

                    messages.append({
                        "id": str(i),
                        "subject": subject,
                        "from": from_field,
                        "to": to_field,
                        "text": body,
                        "html": body,
                        "date": msg.get('Date', '')
                    })

                except Exception as e:
                    print(f"处理邮件 #{i} 时出错: {e}")
                    continue

            # 关闭连接
            mail_server.quit()

            print(f"✅ 2925邮箱: 获取到 {len(messages)} 封匹配邮件")
            if messages:
                print("📧 匹配的邮件列表:")
                for i, msg in enumerate(messages):
                    print(f"  {i+1}. 主题: {msg.get('subject', '无主题')[:50]}")
                    print(f"     发件人: {msg.get('from', '未知')}")
                    print(f"     收件人: {msg.get('to', '未知')}")
            else:
                print("⚠️  没有找到匹配的邮件")

            return messages

        except socket.timeout:
            print("❌ 2925邮箱: 连接超时，可能是网络问题或服务器繁忙")
            print("💡 建议: 请检查网络连接或稍后重试")
            return []
        except socket.gaierror as e:
            print(f"❌ 2925邮箱: DNS解析失败 - {str(e)}")
            print("💡 建议: 请检查网络连接或DNS设置")
            return []
        except poplib.error_proto as e:
            print(f"❌ 2925邮箱: POP3协议错误 - {str(e)}")
            print("💡 建议: 可能是账号密码错误或服务器配置问题")
            return []
        except Exception as e:
            error_msg = str(e)
            print(f"❌ 2925邮箱获取邮件失败: {error_msg}")

            # 根据错误类型提供建议
            if "timed out" in error_msg.lower():
                print("💡 建议: 网络连接超时，请检查网络或稍后重试")
            elif "refused" in error_msg.lower():
                print("💡 建议: 连接被拒绝，可能是防火墙或服务器问题")
            elif "authentication" in error_msg.lower() or "login" in error_msg.lower():
                print("💡 建议: 认证失败，请检查账号密码")
            else:
                print("💡 建议: 请尝试切换到其他邮箱服务")
                import traceback
                print(f"详细错误信息: {traceback.format_exc()}")

            return []

    def get_message_detail(self, message_id, email, token, provider_extra):
        """获取2925邮件详情"""
        # 2925邮箱在get_messages中已经包含了详细信息
        return {"id": message_id, "subject": "邮件详情", "text": "请查看邮件列表"}

    def delete_message(self, message_id, email, token, provider_extra):
        """删除2925邮件"""
        # 2925邮箱不支持通过API删除邮件
        pass



class TempMailGenerator:
    def __init__(self):
        # 设置主题 - 科技感深色主题
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # 创建主窗口（必须在StringVar之前）
        self.root = ctk.CTk()
        self.root.title("🚀 TechMail Generator - 小黄学长搞科研")
        self.root.geometry("480x600")
        self.root.resizable(False, False)

        # 设置窗口图标和样式
        self.root.configure(fg_color=("#f0f0f0", "#0a0a0a"))  # 更深的背景色

        # 添加窗口启动动画效果
        self.root.attributes('-alpha', 0.0)  # 初始透明
        self.animate_window_startup()
        
        # 邮箱服务提供者列表 - 只保留前三个核心服务
        self.providers = [
            Original2925Provider(),  # 优先使用原软件2925服务
            MailTmProvider(get_proxies_func=self.get_proxies),
            MailTmAltProvider(get_proxies_func=self.get_proxies)
        ]
        self.provider_names = [p.get_name() for p in self.providers]
        self.selected_provider_name = tk.StringVar(master=self.root, value=self.provider_names[0])
        self.selected_provider = self.providers[0]

        # 初始化Cursor重置管理器
        self.cursor_reset_manager = CursorResetManager()
        
        # 只在此处初始化result_var
        self.result_var = tk.StringVar(master=self.root)
        
        # 创建主窗口
        # self.root = ctk.CTk()
        # self.root.title("临时邮箱生成器 - Cursor Pro无限续杯")
        # self.root.geometry("800x900")
        # self.root.resizable(False, False)
        
        # 设置字体 - 科技感字体
        self.title_font = ctk.CTkFont(family="Consolas", size=28, weight="bold")
        self.subtitle_font = ctk.CTkFont(family="Consolas", size=16, weight="bold")
        self.text_font = ctk.CTkFont(family="Microsoft YaHei UI", size=13)
        self.small_font = ctk.CTkFont(family="Microsoft YaHei UI", size=11)
        self.mono_font = ctk.CTkFont(family="Consolas", size=12)

        # 科技感配色方案
        self.colors = {
            "primary": "#00d4ff",      # 科技蓝
            "secondary": "#ff6b35",    # 橙色强调
            "success": "#00ff88",      # 成功绿
            "warning": "#ffaa00",      # 警告橙
            "danger": "#ff4757",       # 危险红
            "dark": "#1a1a1a",         # 深色背景
            "card": "#2a2a2a",         # 卡片背景
            "border": "#404040",       # 边框色
            "text": "#ffffff",         # 主文字
            "text_secondary": "#b0b0b0" # 次要文字
        }

        # 语言设置
        self.load_language_setting()  # 加载保存的语言设置
        self.init_language_system()
        
        # 保存的邮箱和历史记录
        self.saved_emails = []
        self.history = []
        self.load_saved_data()
        
        # 邮箱监控状态
        self.monitoring = False
        self.monitor_thread = None
        
        # 当前临时邮箱
        self.current_email = None
        self.current_token = None
        
        # 代理配置
        self.proxy_config = None
        
        # 创建界面
        self.create_widgets()

    def init_language_system(self):
        """初始化语言系统"""
        self.texts = {
            "zh": {
                # 窗口标题
                "window_title": "🚀 TechMail Generator - 小黄学长搞科研",

                # 主标题区域
                "main_title": "Augment Pro",
                "subtitle": "",
                "system_status": "🟢 系统在线",

                # 服务选择区域
                "service_provider": "邮箱服务:",
                "status_ready": "🔄 就绪",
                "status_testing": "🔄 测试中...",
                "status_online": "✅ 服务正常",
                "status_offline": "❌ 服务异常",
                "status_error": "❌ 服务错误",

                # 邮箱生成区域
                "generated_email": "邮箱地址:",
                "btn_generate": "生成",
                "btn_copy": "复制",
                "btn_test": "测试",

                # 标签页
                "tab_monitor": "验证码监控",
                "tab_history": "历史记录",

                # 监控区域
                "monitor_title": "验证码监控",
                "monitor_standby": "待机",
                "monitor_active": "监控中",
                "btn_start_monitor": "开始监控",
                "btn_stop_monitor": "停止监控",
                "btn_clear": "清除",
                "btn_reset_cursor": "重置Cursor",

                # 历史记录
                "history_title": "历史记录:",

                # 底部区域
                "footer_text": "仅供学习使用",
                "btn_help": "帮助",
                "btn_language": "English",

                # 消息提示
                "msg_test_start": "🚀 测试开始",
                "msg_test_success": "🎉 测试成功",
                "msg_test_failed": "❌ 测试失败",
                "msg_monitor_start": "🚀 监控启动",
                "msg_monitor_stop": "⏹️ 监控停止",
                "msg_copy_success": "✅ 复制成功",
                "msg_generate_success": "✅ 生成成功",
                "msg_generate_failed": "❌ 生成失败",

                # 帮助文档
                "help_title": "🚀 TechMail 使用说明",
                "help_content": """🚀 TECHMAIL GENERATOR - 使用说明

═══════════════════════════════════════════════════════════════

🔥 推荐服务（稳定性高）:
• .icu - 原版Cursor工具邮箱服务，最稳定
• mail.tm        - 功能完整，支持删除邮件
• 1secmail       - 简单易用，响应快速
• guerrillamail  - 老牌服务，稳定可靠

⚡ 备用服务:
• tempmail.org   - 临时邮箱服务，多域名支持
• mailsac        - 老牌服务，API稳定
• getnada        - 快速响应，简单易用
• maildrop       - 支持API访问
• 10minutemail   - 10分钟有效期
• mohmal         - 多域名支持
• inboxkitten    - 新兴服务

💡 使用建议:
1. 优先使用推荐服务
2. 如果某个服务不可用，请切换到其他服务
3. 使用"测试"按钮检查服务状态
4. 验证码监控支持多种格式自动识别

⚠️ 注意事项:
• 临时邮箱仅用于接收验证码
• 请勿用于重要账户注册
• 邮件可能会被自动删除
• 部分服务可能因网络问题暂时不可用

🔄 Cursor重置功能:
• 一键重置Cursor机器ID
• 自动关闭Cursor进程
• 清理状态数据库
• 支持无限续杯使用

💡 重置使用说明:
1. 点击"重置Cursor"按钮
2. 确认重置操作
3. 等待重置完成
4. 重新启动Cursor使用

═══════════════════════════════════════════════════════════════
TechMail Generator v2.0 - 集成Cursor重置功能"""
            },
            "en": {
                # 窗口标题
                "window_title": "🚀 TechMail Generator - Advanced Email System",

                # 主标题区域
                "main_title": "📧 Email Generator",
                "subtitle": "Advanced Temporary Email System",
                "system_status": "🟢 SYSTEM ONLINE",

                # 服务选择区域
                "service_provider": "Service:",
                "status_ready": "🔄 READY",
                "status_testing": "🔄 TESTING...",
                "status_online": "✅ ONLINE",
                "status_offline": "❌ OFFLINE",
                "status_error": "❌ ERROR",

                # 邮箱生成区域
                "generated_email": "Email Address:",
                "btn_generate": "Generate",
                "btn_copy": "Copy",
                "btn_test": "Test",

                # 标签页
                "tab_monitor": "Code Monitor",
                "tab_history": "History",

                # 监控区域
                "monitor_title": "Code Monitor",
                "monitor_standby": "Standby",
                "monitor_active": "Monitoring",
                "btn_start_monitor": "Start",
                "btn_stop_monitor": "Stop",
                "btn_clear": "Clear",
                "btn_reset_cursor": "Reset Cursor",

                # 历史记录
                "history_title": "History:",

                # 底部区域
                "footer_text": "For educational use only",
                "btn_help": "Help",
                "btn_language": "中文",

                # 消息提示
                "msg_test_start": "🚀 Test Started",
                "msg_test_success": "🎉 Test Successful",
                "msg_test_failed": "❌ Test Failed",
                "msg_monitor_start": "🚀 Monitor Started",
                "msg_monitor_stop": "⏹️ Monitor Stopped",
                "msg_copy_success": "✅ Copied Successfully",
                "msg_generate_success": "✅ Generated Successfully",
                "msg_generate_failed": "❌ Generation Failed",

                # 帮助文档
                "help_title": "🚀 TechMail Documentation",
                "help_content": """🚀 TECHMAIL GENERATOR - SERVICE DOCUMENTATION

═══════════════════════════════════════════════════════════════

🔥 RECOMMENDED SERVICES (High Stability):
• 2925.com (Original) - Original Cursor tool email service, most stable
• mail.tm        - Full featured, supports message deletion
• 1secmail       - Simple & fast response
• guerrillamail  - Veteran service, highly reliable

⚡ BACKUP SERVICES:
• tempmail.org   - Temporary email service, multi-domain
• mailsac        - Veteran service, stable API
• getnada        - Fast response, easy to use
• maildrop       - API access supported
• 10minutemail   - 10-minute validity
• mohmal         - Multi-domain support
• inboxkitten    - Emerging service

💡 USAGE RECOMMENDATIONS:
1. Prioritize recommended services
2. Switch services if current one is unavailable
3. Use "TEST" button to check service status
4. Monitor supports multiple verification code formats

⚠️ IMPORTANT NOTES:
• Temporary emails are for verification codes only
• Do not use for important account registration
• Emails may be automatically deleted
• Some services may be temporarily unavailable

🔄 Cursor Reset Feature:
• One-click Cursor machine ID reset
• Auto-close Cursor processes
• Clean state database
• Support unlimited usage

💡 Reset Instructions:
1. Click "Reset Cursor" button
2. Confirm reset operation
3. Wait for completion
4. Restart Cursor to use

═══════════════════════════════════════════════════════════════
TechMail Generator v2.0 - With Cursor Reset Feature"""
            }
        }

    def get_text(self, key):
        """获取当前语言的文本"""
        return self.texts[self.current_language].get(key, key)

    def switch_language(self):
        """切换语言"""
        self.current_language = "en" if self.current_language == "zh" else "zh"
        self.update_all_texts()
        self.save_language_setting()

    def save_language_setting(self):
        """保存语言设置"""
        try:
            import json
            settings = {"language": self.current_language}
            with open("language_setting.json", "w", encoding="utf-8") as f:
                json.dump(settings, f, ensure_ascii=False)
        except:
            pass

    def load_language_setting(self):
        """加载语言设置"""
        try:
            import json
            with open("language_setting.json", "r", encoding="utf-8") as f:
                settings = json.load(f)
                self.current_language = settings.get("language", "zh")
        except:
            self.current_language = "zh"

    def update_all_texts(self):
        """更新所有界面文本"""
        # 更新窗口标题
        self.root.title(self.get_text("window_title"))

        # 更新主标题区域
        self.title_label.configure(text=self.get_text("main_title"))
        self.subtitle_label.configure(text=self.get_text("subtitle"))
        self.system_status_label.configure(text=self.get_text("system_status"))

        # 更新服务选择区域
        self.provider_label.configure(text=self.get_text("service_provider"))
        self.service_status_var.set(self.get_text("status_ready"))

        # 更新邮箱生成区域
        self.email_label.configure(text=self.get_text("generated_email"))
        self.generate_button.configure(text=self.get_text("btn_generate"))
        self.copy_button.configure(text=self.get_text("btn_copy"))
        self.test_button.configure(text=self.get_text("btn_test"))

        # 更新标签页（需要重新创建标签页）
        # 这里我们只更新内部元素的文本
        self.monitor_label.configure(text=self.get_text("monitor_title"))
        self.status_var.set(self.get_text("monitor_standby"))
        self.start_monitor_button.configure(text=self.get_text("btn_start_monitor"))
        self.clear_codes_button.configure(text=self.get_text("btn_clear"))
        self.reset_cursor_button.configure(text=self.get_text("btn_reset_cursor"))

        # 更新历史记录
        self.history_title.configure(text=self.get_text("history_title"))
        self.clear_history_button.configure(text=self.get_text("btn_clear"))

        # 更新底部区域
        self.footer_label.configure(text=self.get_text("footer_text"))
        self.help_button.configure(text=self.get_text("btn_help"))
        self.language_button.configure(text=self.get_text("btn_language"))
    
    def create_widgets(self):
        # 主容器 - 科技感设计
        main_container = ctk.CTkFrame(
            self.root,
            fg_color=self.colors["dark"],
            corner_radius=0
        )
        main_container.pack(fill="both", expand=True, padx=0, pady=0)

        # 顶部装饰条
        top_bar = ctk.CTkFrame(
            main_container,
            height=4,
            fg_color=self.colors["primary"],
            corner_radius=0
        )
        top_bar.pack(fill="x", padx=0, pady=0)

        # 标题区域 - 简洁设计
        title_frame = ctk.CTkFrame(
            main_container,
            fg_color="transparent"
        )
        title_frame.pack(fill="x", padx=20, pady=(15, 10))

        # 主标题
        self.title_label = ctk.CTkLabel(
            title_frame,
            text=self.get_text("main_title"),
            font=self.title_font,
            text_color=self.colors["primary"]
        )
        self.title_label.pack()

        # 服务选择区域 - 显示
        provider_frame = ctk.CTkFrame(
            main_container,
            fg_color=self.colors["card"],
            corner_radius=10
        )
        provider_frame.pack(fill="x", padx=20, pady=(5, 10))

        provider_content = ctk.CTkFrame(provider_frame, fg_color="transparent")
        provider_content.pack(fill="x", padx=15, pady=12)

        # 服务选择和状态在同一行
        selection_frame = ctk.CTkFrame(provider_content, fg_color="transparent")
        selection_frame.pack(fill="x")

        self.provider_label = ctk.CTkLabel(
            selection_frame,
            text=self.get_text("service_provider"),
            font=self.text_font,
            text_color=self.colors["text"]
        )
        self.provider_label.pack(side="left")

        self.provider_option = ctk.CTkOptionMenu(
            selection_frame,
            variable=self.selected_provider_name,
            values=self.provider_names,
            command=self.on_provider_change,
            font=self.text_font,
            width=180,
            height=32,
            fg_color=self.colors["primary"],
            button_color=self.colors["primary"],
            button_hover_color=self.colors["secondary"],
            corner_radius=6
        )
        self.provider_option.pack(side="left", padx=(10, 0))

        # 测试服务按钮
        self.test_button = ctk.CTkButton(
            selection_frame,
            text=self.get_text("btn_test"),
            command=self.test_current_provider,
            font=self.small_font,
            height=32,
            width=80,
            fg_color=self.colors["warning"],
            hover_color="#e69500",
            corner_radius=6
        )
        self.test_button.pack(side="right")

        # 状态显示（保留但简化）
        status_frame = ctk.CTkFrame(
            main_container,
            fg_color="transparent"
        )
        status_frame.pack(fill="x", padx=20, pady=(5, 5))
        
        self.service_status_var = tk.StringVar(value=self.get_text("status_ready"))
        self.service_status_label = ctk.CTkLabel(
            status_frame,
            textvariable=self.service_status_var,
            font=self.small_font,
            text_color=self.colors["warning"]
        )
        self.service_status_label.pack()

        # 邮箱显示区 - 简洁设计
        email_frame = ctk.CTkFrame(
            main_container,
            fg_color=self.colors["card"],
            corner_radius=10
        )
        email_frame.pack(fill="x", padx=20, pady=(0, 10))

        email_content = ctk.CTkFrame(email_frame, fg_color="transparent")
        email_content.pack(fill="x", padx=15, pady=15)

        self.email_label = ctk.CTkLabel(
            email_content,
            text=self.get_text("generated_email"),
            font=self.text_font,
            text_color=self.colors["text"]
        )
        self.email_label.pack(anchor="w", pady=(0, 8))

        # 邮箱输入框
        result_entry = ctk.CTkEntry(
            email_content,
            textvariable=self.result_var,
            state="readonly",
            height=36,
            font=self.text_font,
            corner_radius=6,
            fg_color=self.colors["dark"],
            border_color=self.colors["border"],
            border_width=1,
            text_color=self.colors["success"]
        )
        result_entry.pack(fill="x", pady=(0, 10))
        # 按钮组 - 简洁设计
        button_frame = ctk.CTkFrame(email_content, fg_color="transparent")
        button_frame.pack(fill="x")

        self.generate_button = ctk.CTkButton(
            button_frame,
            text=self.get_text("btn_generate"),
            command=self.generate_email,
            font=self.text_font,
            height=36,
            fg_color=self.colors["success"],
            hover_color="#00cc77",
            corner_radius=6
        )
        self.generate_button.pack(side="left", expand=True, fill="x", padx=(0, 5))

        self.copy_button = ctk.CTkButton(
            button_frame,
            text=self.get_text("btn_copy"),
            command=self.copy_email,
            font=self.text_font,
            height=36,
            fg_color=self.colors["primary"],
            hover_color="#00b8e6",
            corner_radius=6
        )
        self.copy_button.pack(side="left", expand=True, fill="x", padx=(0, 0))

        # 隐藏测试按钮
        # self.test_button = ctk.CTkButton(
        #     button_frame,
        #     text=self.get_text("btn_test"),
        #     command=self.test_current_provider,
        #     font=self.text_font,
        #     height=36,
        #     fg_color=self.colors["warning"],
        #     hover_color="#e69500",
        #     corner_radius=6
        # )
        # self.test_button.pack(side="left", expand=True, fill="x", padx=(5, 0))

        # Tabview: 验证码监控 & 历史记录 - 简洁设计
        tabview = ctk.CTkTabview(
            main_container,
            height=180,
            fg_color=self.colors["card"],
            segmented_button_fg_color=self.colors["dark"],
            segmented_button_selected_color=self.colors["primary"],
            corner_radius=10
        )
        tabview.pack(fill="both", expand=True, padx=20, pady=(0, 10))

        self.code_tab = tabview.add(self.get_text("tab_monitor"))
        self.history_tab = tabview.add(self.get_text("tab_history"))

        # 验证码监控Tab - 简洁设计
        monitor_content = ctk.CTkFrame(self.code_tab, fg_color="transparent")
        monitor_content.pack(fill="both", expand=True, padx=10, pady=10)

        # 状态和按钮在同一行
        control_frame = ctk.CTkFrame(monitor_content, fg_color="transparent")
        control_frame.pack(fill="x", pady=(0, 8))

        self.status_var = tk.StringVar(value=self.get_text("monitor_standby"))
        self.status_label = ctk.CTkLabel(
            control_frame,
            textvariable=self.status_var,
            font=self.text_font,
            text_color=self.colors["warning"]
        )
        self.status_label.pack(side="left")

        # 按钮组
        button_group = ctk.CTkFrame(control_frame, fg_color="transparent")
        button_group.pack(side="right")

        self.start_monitor_button = ctk.CTkButton(
            button_group,
            text=self.get_text("btn_start_monitor"),
            command=self.toggle_monitor,
            font=self.small_font,
            height=32,
            width=100,
            fg_color=self.colors["success"],
            hover_color="#00cc77",
            corner_radius=6
        )
        self.start_monitor_button.pack(side="left", padx=(0, 5))

        self.clear_codes_button = ctk.CTkButton(
            button_group,
            text=self.get_text("btn_clear"),
            command=self.clear_verification_codes,
            font=self.small_font,
            height=32,
            width=80,
            fg_color=self.colors["danger"],
            hover_color="#e63946",
            corner_radius=6
        )
        self.clear_codes_button.pack(side="left", padx=(0, 5))

        # Cursor重置按钮
        self.reset_cursor_button = ctk.CTkButton(
            button_group,
            text=self.get_text("btn_reset_cursor"),
            command=self.reset_cursor,
            font=self.small_font,
            height=32,
            width=100,
            fg_color=self.colors["warning"],
            hover_color="#e69500",
            corner_radius=6
        )
        self.reset_cursor_button.pack(side="left")
        # 验证码显示区
        self.codes_text = ctk.CTkTextbox(
            monitor_content,
            font=self.text_font,
            corner_radius=6,
            fg_color=self.colors["dark"],
            border_color=self.colors["border"],
            border_width=1,
            text_color=self.colors["success"]
        )
        self.codes_text.pack(fill="both", expand=True)

        # 历史记录Tab - 简洁设计
        history_content = ctk.CTkFrame(self.history_tab, fg_color="transparent")
        history_content.pack(fill="both", expand=True, padx=10, pady=10)

        # 标题和清除按钮
        history_header = ctk.CTkFrame(history_content, fg_color="transparent")
        history_header.pack(fill="x", pady=(0, 8))

        self.history_title = ctk.CTkLabel(
            history_header,
            text=self.get_text("history_title"),
            font=self.text_font,
            text_color=self.colors["text"]
        )
        self.history_title.pack(side="left")

        self.clear_history_button = ctk.CTkButton(
            history_header,
            text=self.get_text("btn_clear"),
            command=self.clear_history,
            font=self.small_font,
            height=32,
            width=80,
            fg_color=self.colors["danger"],
            hover_color="#e63946",
            corner_radius=6
        )
        self.clear_history_button.pack(side="right")

        # 历史记录文本框
        self.history_text = ctk.CTkTextbox(
            history_content,
            font=self.text_font,
            corner_radius=6,
            fg_color=self.colors["dark"],
            border_color=self.colors["border"],
            border_width=1,
            text_color=self.colors["text_secondary"]
        )
        self.history_text.pack(fill="both", expand=True)

        # 底部区域 - 简洁设计
        footer_frame = ctk.CTkFrame(main_container, fg_color="transparent")
        footer_frame.pack(fill="x", padx=20, pady=(5, 10))

        # 语言切换按钮
        self.language_button = ctk.CTkButton(
            footer_frame,
            text=self.get_text("btn_language"),
            command=self.switch_language,
            font=self.small_font,
            height=28,
            width=80,
            fg_color=self.colors["secondary"],
            hover_color="#e55a2b",
            corner_radius=6
        )
        self.language_button.pack(side="left")

        # 帮助按钮
        self.help_button = ctk.CTkButton(
            footer_frame,
            text=self.get_text("btn_help"),
            command=self.show_service_help,
            font=self.small_font,
            height=28,
            width=80,
            fg_color=self.colors["primary"],
            hover_color=self.colors["secondary"],
            corner_radius=6
        )
        self.help_button.pack(side="left", padx=(8, 0))

        # 版权信息
        self.footer_label = ctk.CTkLabel(
            footer_frame,
            text=self.get_text("footer_text"),
            font=self.small_font,
            text_color=self.colors["text_secondary"]
        )
        self.footer_label.pack(side="right")

        # 更新显示
        self.update_history_display()
        self.update_saved_display()
        
        # 代理设置（隐藏）
        self.proxy_var = tk.StringVar()  # 保留变量以免报错
        # proxy_frame = ctk.CTkFrame(
        #     self.root,
        #     fg_color=self.colors["card"],
        #     corner_radius=10
        # )
        # proxy_frame.pack(fill="x", padx=20, pady=(10, 0))
        # proxy_label = ctk.CTkLabel(
        #     proxy_frame,
        #     text="代理 (如 127.0.0.1:1080, 可选):",
        #     font=self.text_font,
        #     text_color=self.colors["text"]
        # )
        # proxy_label.pack(side="left", padx=(10, 5))
        # proxy_entry = ctk.CTkEntry(
        #     proxy_frame,
        #     textvariable=self.proxy_var,
        #     width=180,
        #     font=self.text_font,
        #     corner_radius=6,
        #     fg_color=self.colors["dark"],
        #     border_color=self.colors["border"],
        #     border_width=1,
        #     text_color=self.colors["text_secondary"]
        # )
        # proxy_entry.pack(side="left", padx=(0, 10))
    
    def generate_email(self):
        """生成邮箱（后台线程执行，避免界面卡顿）"""
        def generate_in_background():
            try:
                print("\n" + "="*50)
                print("开始生成新的临时邮箱")
                print("="*50)
                
                provider = self.selected_provider
                email = password = token = None
                provider_extra = None
                
                # 更新状态为生成中
                self.root.after(0, lambda: self.service_status_var.set("🔄 生成中..."))
                self.root.after(0, lambda: self.generate_button.configure(state="disabled", text="生成中..."))
                
                # 生成邮箱
                email, password, token, provider_extra = provider.generate_email()
                print(f"生成临时邮箱: {email}")
                if password:
                    print(f"生成密码: {password}")
                
                # 保存邮箱信息
                self.current_email = email
                self.current_token = token
                self.current_provider = provider.get_name()
                self.current_provider_extra = provider_extra
                
                # 在主线程中更新UI
                self.root.after(0, lambda: self.result_var.set(email))
                
                # 添加到历史记录
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.history.append({
                    "time": current_time,
                    "email": email,
                    "provider": self.current_provider,
                    "provider_extra": provider_extra
                })
                
                # 更新显示
                self.root.after(0, self.update_history_display)
                # 保存数据
                self.root.after(0, self.save_data)
                self.root.after(0, lambda: self.service_status_var.set(self.get_text("msg_generate_success")))
                
            except Exception as e:
                print(f"生成邮箱时出错: {str(e)}")
                self.root.after(0, lambda: self.service_status_var.set(f"{self.get_text('msg_generate_failed')}: {str(e)}"))
            finally:
                # 恢复按钮状态
                self.root.after(0, lambda: self.generate_button.configure(state="normal", text=self.get_text("btn_generate")))
        
        # 在后台线程中执行邮箱生成
        thread = threading.Thread(target=generate_in_background, daemon=True)
        thread.start()
    
    def copy_email(self):
        email = self.result_var.get()
        if email:
            pyperclip.copy(email)
            self.service_status_var.set(self.get_text("msg_copy_success"))
        else:
            self.service_status_var.set("Please generate an email first" if self.current_language == "en" else "请先生成一个邮箱")
    
    # 移除 save_email 方法
    
    def clear_history(self):
        if messagebox.askyesno("确认", "确定要清除所有历史记录吗?"):
            self.history = []
            self.update_history_display()
            self.save_data()
            self.service_status_var.set("历史记录已清除" if self.current_language == "zh" else "History cleared")
    
    def clear_saved(self):
        if messagebox.askyesno("确认", "确定要清除所有已保存的账号吗?"):
            self.saved_accounts = []
            self.update_saved_display()
            self.save_data()
            self.service_status_var.set("已保存账号已清除" if self.current_language == "zh" else "Saved accounts cleared")
    
    def export_saved(self):
        if not self.saved_accounts:
            self.service_status_var.set("没有已保存的账号" if self.current_language == "zh" else "No saved accounts")
            return
        
        # 导出为文本文件
        try:
            with open("cursor_pro_accounts.txt", "w", encoding="utf-8") as f:
                f.write("Cursor Pro 账号列表\n")
                f.write("=" * 50 + "\n\n")
                
                for i, saved in enumerate(self.saved_accounts, 1):
                    f.write(f"{i}. 邮箱: {saved['email']}\n")
                    f.write(f"   密码: {saved['password']}\n")
                    if saved['note']:
                        f.write(f"   备注: {saved['note']}\n")
                    f.write(f"   保存时间: {saved['time']}\n\n")
            
            self.service_status_var.set("账号已导出到 cursor_pro_accounts.txt" if self.current_language == "zh" else "Accounts exported to cursor_pro_accounts.txt")
        except Exception as e:
            self.service_status_var.set(f"导出失败: {str(e)}" if self.current_language == "zh" else f"Export failed: {str(e)}")
    
    def toggle_monitor(self):
        if not self.monitoring:
            if not self.current_email:
                self.service_status_var.set("请先生成一个临时邮箱" if self.current_language == "zh" else "Please generate an email first")
                return
            # 开始监控
            self.monitoring = True
            self.start_monitor_button.configure(text=self.get_text("btn_stop_monitor"), fg_color=self.colors["danger"], state="normal")
            self.status_var.set(f"{self.get_text('monitor_active')} (100s)")
            # 启动监控线程
            self.monitor_thread = threading.Thread(target=self.monitor_emails, daemon=True)
            self.monitor_thread.start()
            # 启动倒计时线程
            self.countdown_thread = threading.Thread(target=self.monitor_countdown, daemon=True)
            self.countdown_thread.start()
            self.service_status_var.set(self.get_text("msg_monitor_start"))
        else:
            # 停止监控
            self.monitoring = False
            self.start_monitor_button.configure(text=self.get_text("btn_start_monitor"), fg_color=self.colors["success"], state="normal")
            self.status_var.set(self.get_text("monitor_standby"))
            self.service_status_var.set(self.get_text("msg_monitor_stop"))

    def monitor_countdown(self):
        seconds = 100
        while seconds > 0 and self.monitoring:
            self.root.after(0, lambda s=seconds: self.status_var.set(f"{self.get_text('monitor_active')} ({s}s)"))
            # 保持按钮可用，让用户可以手动停止
            self.root.after(0, lambda s=seconds: self.start_monitor_button.configure(text=f"{self.get_text('btn_stop_monitor')} ({s}s)", state="normal"))
            time.sleep(1)
            seconds -= 1
        if self.monitoring:
            self.root.after(0, self.toggle_monitor)
    
    def monitor_emails(self):
        while self.monitoring:
            try:
                if not self.current_email or not hasattr(self, 'current_provider'):
                    print("没有可用的临时邮箱")
                    self.root.after(0, lambda: self.status_var.set("请先生成邮箱"))
                    break
                provider = None
                provider_extra = getattr(self, 'current_provider_extra', None)
                for p in self.providers:
                    if p.get_name() == self.current_provider:
                        provider = p
                        break
                if not provider:
                    print("找不到对应的邮箱服务provider")
                    self.root.after(0, lambda: self.status_var.set("邮箱服务错误"))
                    break
                print("\n" + "="*50)
                print(f"开始检查邮箱: {self.current_email} [{self.current_provider}]")
                print("="*50)
                # 获取邮件列表
                emails = provider.get_messages(self.current_email, self.current_token, provider_extra)
                print(f"找到 {len(emails)} 封邮件")
                for email_item in emails:
                    try:
                        # mail.tm: id, 1secmail: id
                        email_id = email_item.get("id") if isinstance(email_item, dict) else email_item["id"]
                        if not email_id:
                            continue

                        # 2925邮箱服务已经在get_messages中返回完整内容，不需要再调用get_message_detail
                        if self.current_provider == ".icu":
                            mail_detail = email_item  # 直接使用email_item作为邮件详情
                        else:
                            mail_detail = provider.get_message_detail(email_id, self.current_email, self.current_token, provider_extra)
                            if not mail_detail:
                                continue

                        # 兼容不同服务的字段
                        subject = mail_detail.get("subject", "")
                        sender = mail_detail.get("from", "")
                        content = mail_detail.get("text", "")

                        # 不同服务的字段兼容
                        if self.current_provider in ["mail.tm", "mail.tm (备用)"]:
                            subject = mail_detail.get("subject", "")
                            sender = mail_detail.get("from", {}).get("address", "") if isinstance(mail_detail.get("from"), dict) else str(mail_detail.get("from", ""))
                            content = mail_detail.get("text", mail_detail.get("html", ""))
                        elif self.current_provider == ".icu":
                            # 2925邮箱服务的字段处理
                            subject = mail_detail.get("subject", "")
                            sender = mail_detail.get("from", "")
                            content = mail_detail.get("text", mail_detail.get("html", ""))
                        print(f"\n邮件主题: {subject}")
                        print(f"发件人: {sender}")
                        print(f"内容预览: {content[:200]}")
                        # 提取验证码 - 针对2925邮箱和其他服务优化
                        text_to_check = f"{subject}\n{content}"
                        patterns = [
                            # 2925邮箱特定模式（优先级最高）
                            r'验证码[是为]?[:：\s]*?(\d{6})',
                            r'verification code[:\s]*?(\d{6})',
                            r'code[:\s]*?(\d{6})',
                            r'[验證]证码[是为]?[：:]*?(\d{6})',
                            r'[Cc]ode:?\s*(\d{6})',
                            r'您的验证码是[：:\s]*?(\d{6})',
                            r'[Yy]our verification code is[：:\s]*?(\d{6})',
                            r'[Yy]our code is[：:\s]*?(\d{6})',

                            # Cursor和Augment特定模式
                            r'[Yy]our Cursor verification code is[：:\s]*?(\d{6})',
                            r'Cursor[^<>]*?code[^<>]*?(\d{6})',
                            r'[Yy]our Augment verification code is[：:\s]*?(\d{6})',
                            r'Augment[^<>]*?code[^<>]*?(\d{6})',
                            r'Welcome to Augment[^<>]*?(\d{6})',
                            r'verification code for Augment[：:\s]*?(\d{6})',

                            # 中文验证码模式
                            r'验证码[是为]?[:：\s]*([0-9]{4,8})',
                            r'[验证确认]码[\s:：]*([0-9]{4,8})',
                            r'验证码：*\s*([0-9]{4,8})',
                            r'动态码[是为]?[:：\s]*([0-9]{4,8})',
                            r'校验码[是为]?[:：\s]*([0-9]{4,8})',
                            r'验证代码[是为]?[:：\s]*([0-9]{4,8})',
                            r'动态密码[是为]?[:：\s]*([0-9]{4,8})',
                            r'安全码[是为]?[:：\s]*([0-9]{4,8})',

                            # 英文验证码模式
                            r'verification\s*code[:\s]*([0-9]{4,8})',
                            r'confirm\s*code[:\s]*([0-9]{4,8})',
                            r'security\s*code[:\s]*([0-9]{4,8})',
                            r'auth\s*code[:\s]*([0-9]{4,8})',
                            r'otp[:\s]*([0-9]{4,8})',
                            r'pin[:\s]*([0-9]{4,8})',
                            r'code[:\s]*([0-9]{4,8})',

                            # 通用数字模式（排除CSS颜色代码）
                            r'(?<![#a-fA-F0-9])([0-9]{6})(?![a-fA-F0-9])',  # 6位数字（优先）
                            r'(?<![#a-fA-F0-9])([0-9]{4})(?![a-fA-F0-9])',  # 4位数字
                            r'(?<![#a-fA-F0-9])([0-9]{5})(?![a-fA-F0-9])',  # 5位数字
                            r'(?<![#a-fA-F0-9])([0-9]{8})(?![a-fA-F0-9])',  # 8位数字

                            # 最后的备用模式
                            r'[^0-9]([0-9]{6})[^0-9]',
                            r'[^0-9]([0-9]{4})[^0-9]',
                            r'[^0-9]([0-9]{5})[^0-9]',
                            r'[^0-9]([0-9]{8})[^0-9]'
                        ]
                        verification_code = None
                        for pattern in patterns:
                            matches = re.findall(pattern, text_to_check)
                            if matches:
                                verification_code = matches[0]
                                print(f"找到验证码: {verification_code}")
                                break
                        if verification_code:
                            self.root.after(0, self.add_verification_code, verification_code, subject, sender, self.current_email)
                            self.root.after(0, lambda: self.status_var.set("正常运行中"))
                        # 删除已读邮件（仅mail.tm支持）
                        try:
                            provider.delete_message(email_id, self.current_email, self.current_token, provider_extra)
                        except Exception:
                            pass
                    except Exception as e:
                        print(f"处理邮件时出错: {str(e)}")
                        continue
                self.root.after(0, lambda: self.status_var.set("检查完成"))
            except Exception as e:
                error_msg = str(e)
                print(f"监控邮件时出错: {error_msg}")
                self.root.after(0, lambda: self.status_var.set(f"连接错误: {error_msg[:20]}..."))
            print("\n" + "="*50)
            print("等待5秒后重新检查...")
            print("="*50)
            for _ in range(5):
                if not self.monitoring:
                    break
                time.sleep(1)
    
    def add_verification_code(self, code, subject, sender, email):
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 在文本框顶部添加新验证码
        self.codes_text.insert("0.0", f"验证码: {code}\n")
        self.codes_text.insert("0.0", f"时间: {current_time}\n")
        self.codes_text.insert("0.0", f"主题: {subject}\n")
        self.codes_text.insert("0.0", f"发件人: {sender}\n")
        if email:
            self.codes_text.insert("0.0", f"收件邮箱: {email}\n")
        self.codes_text.insert("0.0", "-" * 50 + "\n\n")
        
        # 复制验证码到剪贴板
        pyperclip.copy(code)
        
        # 弹出通知
        self.service_status_var.set(f"验证码 {code} 已复制到剪贴板" if self.current_language == "zh" else f"Code {code} copied to clipboard")
    
    def clear_verification_codes(self):
        self.codes_text.delete("0.0", "end")

    def reset_cursor(self):
        """重置Cursor功能"""
        # 显示确认对话框
        result = messagebox.askyesno(
            "确认重置",
            "确定要重置Cursor吗？\n\n此操作将：\n• 关闭所有Cursor进程\n• 删除状态数据库\n• 重置机器ID\n\n是否继续？",
            icon='warning'
        )

        if not result:
            return

        # 在后台线程中执行重置
        def reset_in_background():
            try:
                # 更新状态
                self.root.after(0, lambda: self.service_status_var.set("🔄 正在重置Cursor..."))
                self.root.after(0, lambda: self.reset_cursor_button.configure(state="disabled", text="重置中..."))

                # 显示重置过程
                self.root.after(0, lambda: self.codes_text.insert("end", "🔄 开始重置Cursor...\n"))

                # 执行重置操作
                success = self.cursor_reset_manager.reset_machine_id()

                if success:
                    # 重置成功
                    success_msg = "✅ Cursor重置成功！\n• 所有进程已关闭\n• 状态数据库已清理\n• 机器ID已重置\n\n请重新启动Cursor使用。\n"
                    self.root.after(0, lambda: self.codes_text.insert("end", success_msg))
                    self.root.after(0, lambda: self.service_status_var.set("✅ Cursor重置成功"))

                    # 显示成功消息框
                    self.root.after(0, lambda: messagebox.showinfo(
                        "重置成功",
                        "Cursor已成功重置！\n\n请重新启动Cursor以使用新的机器ID。",
                        icon='info'
                    ))
                else:
                    # 重置失败
                    error_msg = "❌ Cursor重置失败\n请查看控制台输出了解详细错误信息。\n"
                    self.root.after(0, lambda: self.codes_text.insert("end", error_msg))
                    self.root.after(0, lambda: self.service_status_var.set("❌ Cursor重置失败"))

                    # 显示错误消息框
                    self.root.after(0, lambda: messagebox.showerror(
                        "重置失败",
                        "Cursor重置失败！\n\n请检查权限设置或手动重置。",
                        icon='error'
                    ))

            except Exception as e:
                error_msg = f"❌ 重置过程出错: {str(e)}\n"
                print(f"重置Cursor时发生错误: {e}")
                self.root.after(0, lambda: self.codes_text.insert("end", error_msg))
                self.root.after(0, lambda: self.service_status_var.set("❌ 重置出错"))

                # 显示错误消息框
                self.root.after(0, lambda: messagebox.showerror(
                    "重置错误",
                    f"重置过程中发生错误：\n{str(e)}",
                    icon='error'
                ))

            finally:
                # 恢复按钮状态
                self.root.after(0, lambda: self.reset_cursor_button.configure(state="normal", text=self.get_text("btn_reset_cursor")))

        # 启动后台线程
        threading.Thread(target=reset_in_background, daemon=True).start()
    
    def update_history_display(self):
        self.history_text.delete("0.0", "end")
        
        if not self.history:
            self.history_text.insert("0.0", "暂无历史记录")
            return
        
        for i, item in enumerate(reversed(self.history), 1):
            self.history_text.insert("end", f"{i}. {item['email']}\n")
            self.history_text.insert("end", f"   时间: {item['time']}\n")
            if item.get("provider"):
                self.history_text.insert("end", f"   服务: {item['provider']}\n")
            if item.get("provider_extra"):
                self.history_text.insert("end", f"   额外信息: {item['provider_extra']}\n")
            self.history_text.insert("end", f"\n")
    
    def update_saved_display(self):
        # 移除已保存邮箱显示
        pass
    
    def load_saved_data(self):
        try:
            if os.path.exists("temp_email_data.json"):
                with open("temp_email_data.json", "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.history = data.get("history", [])
                    # self.saved_emails = data.get("saved_emails", []) # 移除已保存邮箱加载
        except Exception:
            pass
    
    def save_data(self):
        try:
            # 限制历史记录数量
            if len(self.history) > 100:
                self.history = self.history[-100:]
            data = {
                "history": self.history
                # "saved_emails": self.saved_emails # 移除已保存邮箱保存
            }
            with open("temp_email_data.json", "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception:
            pass
    
    def run(self):
        self.root.mainloop()

    def test_current_provider(self):
        """测试当前选择的邮箱服务是否可用（后台线程执行）"""
        def test_in_background():
            try:
                provider = self.selected_provider
                provider_name = provider.get_name()

                # 更新状态为测试中
                self.root.after(0, lambda: self.service_status_var.set("🔄 测试中..."))
                self.root.after(0, lambda: self.test_button.configure(state="disabled", text="测试中..."))

                print(f"\n🔄 开始测试邮箱服务: {provider_name}")

                # 特殊处理2925邮箱服务
                if provider_name == ".icu":
                    # 先测试网络连接
                    import socket
                    try:
                        print("🔄 测试2925服务器连接...")
                        socket.setdefaulttimeout(5)
                        with socket.create_connection(('pop.2925.com', 995), timeout=5):
                            print("✅ 2925服务器连接正常")
                    except Exception as conn_e:
                        print(f"❌ 2925服务器连接测试失败: {conn_e}")
                        self.root.after(0, lambda: self.service_status_var.set(f"❌ 2925 CONNECTION FAILED | 网络连接问题"))
                        return

                # 尝试生成邮箱
                print("🔄 测试邮箱生成...")
                email, password, token, provider_extra = provider.generate_email()
                print(f"✅ 邮箱生成成功: {email}")

                # 对于2925服务，额外测试邮件获取
                if provider_name == ".icu":
                    print("🔄 测试邮件获取功能...")
                    messages = provider.get_messages(email, token, provider_extra)
                    print(f"✅ 邮件获取测试完成，当前邮件数: {len(messages)}")

                if email:
                    # 测试成功
                    success_msg = f"✅ {provider_name.upper()} ONLINE | {self.get_text('msg_test_success')}"
                    self.root.after(0, lambda: self.service_status_var.set(success_msg))
                    print(f"✅ {provider_name} 服务测试通过")
                else:
                    fail_msg = f"❌ {provider_name.upper()} OFFLINE | {self.get_text('msg_test_failed')}"
                    self.root.after(0, lambda: self.service_status_var.set(fail_msg))
                    print(f"❌ {provider_name} 服务测试失败")

            except Exception as e:
                error_msg = str(e)
                print(f"❌ {provider_name} 服务测试异常: {error_msg}")

                # 根据错误类型提供更详细的信息
                if "timed out" in error_msg.lower() or "timeout" in error_msg.lower():
                    status_msg = f"❌ {provider_name.upper()} TIMEOUT | 连接超时"
                elif "refused" in error_msg.lower():
                    status_msg = f"❌ {provider_name.upper()} REFUSED | 连接被拒绝"
                elif "authentication" in error_msg.lower() or "login" in error_msg.lower():
                    status_msg = f"❌ {provider_name.upper()} AUTH FAILED | 认证失败"
                else:
                    status_msg = f"❌ {provider_name.upper()} ERROR | {error_msg[:30]}..."

                self.root.after(0, lambda: self.service_status_var.set(status_msg))
            finally:
                # 恢复按钮状态
                self.root.after(0, lambda: self.test_button.configure(state="normal", text=self.get_text("btn_test")))
        
        # 在后台线程中执行测试
        thread = threading.Thread(target=test_in_background, daemon=True)
        thread.start()

    def on_provider_change(self, value):
        for p in self.providers:
            if p.get_name() == value:
                self.selected_provider = p
                # 重置服务状态
                self.service_status_var.set("🔄 READY")
                break

    def show_service_help(self):
        """显示邮箱服务说明"""
        help_text = """� TECHMAIL GENERATOR - SERVICE DOCUMENTATION

═══════════════════════════════════════════════════════════════

🔥 RECOMMENDED SERVICES (High Stability):
• mail.tm        - Full featured, supports message deletion
• 1secmail       - Simple & fast response
• guerrillamail  - Veteran service, highly reliable

⚡ BACKUP SERVICES:
• tempmail.org   - Temporary email service
• maildrop       - API access supported
• yopmail        - Well-known temp mail
• mohmal         - Multi-domain support
• tempmail.plus  - Emerging service
• 10minutemail   - 10-minute validity
• throwawaymail  - Disposable email

💡 USAGE RECOMMENDATIONS:
1. Prioritize recommended services
2. Switch services if current one is unavailable
3. Use "TEST" button to check service status
4. Monitor supports multiple verification code formats

⚠️ IMPORTANT NOTES:
• Temporary emails are for verification codes only
• Do not use for important account registration
• Emails may be automatically deleted
• Some services may be temporarily unavailable

═══════════════════════════════════════════════════════════════
TechMail Generator v2.0 - Advanced Email System"""

        # 创建帮助窗口 - 科技感设计
        help_window = ctk.CTkToplevel(self.root)
        help_window.title("🚀 TechMail Documentation")
        help_window.geometry("600x700")
        help_window.resizable(False, False)
        help_window.configure(fg_color=self.colors["dark"])

        # 设置窗口居中
        help_window.transient(self.root)
        help_window.grab_set()

        # 顶部装饰条
        top_bar = ctk.CTkFrame(
            help_window,
            height=4,
            fg_color=self.colors["primary"],
            corner_radius=0
        )
        top_bar.pack(fill="x")

        # 标题区域
        title_frame = ctk.CTkFrame(help_window, fg_color="transparent")
        title_frame.pack(fill="x", padx=20, pady=(20, 10))

        title_label = ctk.CTkLabel(
            title_frame,
            text="📖 SYSTEM DOCUMENTATION",
            font=self.title_font,
            text_color=self.colors["primary"]
        )
        title_label.pack()

        # 添加文本框显示帮助内容
        help_textbox = ctk.CTkTextbox(
            help_window,
            font=self.mono_font,
            corner_radius=12,
            fg_color=self.colors["card"],
            border_color=self.colors["border"],
            border_width=1,
            text_color=self.colors["text"]
        )
        help_textbox.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        help_textbox.insert("0.0", help_text)
        help_textbox.configure(state="disabled")

        # 关闭按钮
        close_button = ctk.CTkButton(
            help_window,
            text="🔙 CLOSE",
            command=help_window.destroy,
            font=self.text_font,
            height=42,
            width=120,
            fg_color=self.colors["danger"],
            hover_color="#e63946",
            corner_radius=10
        )
        close_button.pack(pady=(0, 20))

    def animate_window_startup(self):
        """窗口启动动画效果"""
        def fade_in(alpha=0.0):
            if alpha < 1.0:
                alpha += 0.05
                self.root.attributes('-alpha', alpha)
                self.root.after(20, lambda: fade_in(alpha))
            else:
                self.root.attributes('-alpha', 1.0)

        # 延迟启动动画，确保窗口完全加载
        self.root.after(100, fade_in)

    def get_proxies(self):
        proxy = self.proxy_var.get().strip()
        if proxy:
            return {
                "http": f"socks5h://{proxy}",
                "https": f"socks5h://{proxy}"
            }
        return None

if __name__ == "__main__":
    app = TempMailGenerator()
    app.run()