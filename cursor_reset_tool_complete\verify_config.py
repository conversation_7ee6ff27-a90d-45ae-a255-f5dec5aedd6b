#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
verify_config.py - 验证工具配置文件 (硬编码版本)
"""

import os
from pathlib import Path

# 硬编码Gitee仓库信息
GITEE_USERNAME = "flzt_1"
REPO_NAME = "blacklist2.json"
BRANCH = "master"
FILE_PATH = "blacklist2.json"

def get_config_path():
    """获取配置文件路径 (保留但不再使用)"""
    # 不再使用配置文件，但保留函数以维持兼容性
    user_home = str(Path.home())
    config_dir = os.path.join(user_home, "Documents", "FLZT_Cursor_Auth")
    return os.path.join(config_dir, "verify_config.ini")

def get_gitee_urls():
    """获取Gitee URL配置"""
    # 使用硬编码的值构建URL
    api_url = f"https://gitee.com/api/v5/repos/{GITEE_USERNAME}/{REPO_NAME}/contents/{FILE_PATH}"
    raw_url = f"https://gitee.com/{GITEE_USERNAME}/{REPO_NAME}/raw/{BRANCH}/{FILE_PATH}"
    
    return {
        "api_url": api_url,
        "raw_url": raw_url
    }

def get_domain_urls():
    """获取domain.json的URL配置"""
    # 使用与blacklist2.json相同的仓库，但文件名不同
    api_url = f"https://gitee.com/api/v5/repos/{GITEE_USERNAME}/{REPO_NAME}/contents/domain.json"
    raw_url = f"https://gitee.com/{GITEE_USERNAME}/{REPO_NAME}/raw/{BRANCH}/domain.json"
    
    return {
        "api_url": api_url,
        "raw_url": raw_url
    }

def is_auto_update_enabled():
    """检查是否启用自动更新"""
    # 强制返回True，使自动更新始终启用
    return True

if __name__ == "__main__":
    # 显示硬编码的URL信息
    print("配置信息 (硬编码):")
    print(f"Gitee用户名: {GITEE_USERNAME}")
    print(f"仓库名: {REPO_NAME}")
    
    # 获取URL
    urls = get_gitee_urls()
    print("API URL:", urls["api_url"])
    print("Raw URL:", urls["raw_url"]) 