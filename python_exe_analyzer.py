#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python打包文件分析工具
专门用于分析PyInstaller等工具打包的Python程序
"""

import os
import sys
import struct
import re
import zlib
import marshal

def find_python_signatures(filename):
    """查找Python相关的特征签名"""
    signatures = {
        b'PYZ-00': 'PyInstaller PYZ archive',
        b'MEI\x0c\x0b\x0a\x0b\x0e': 'PyInstaller bootloader',
        b'python': 'Python string',
        b'PyInstaller': 'PyInstaller',
        b'_pyi_': 'PyInstaller internal',
        b'pyi_': 'PyInstaller prefix',
        b'site-packages': 'Python site-packages',
        b'.pyc': 'Python compiled',
        b'.pyo': 'Python optimized',
        b'__pycache__': 'Python cache',
        b'import ': 'Python import statement',
        b'def ': 'Python function definition',
        b'class ': 'Python class definition'
    }
    
    found_signatures = []
    
    with open(filename, 'rb') as f:
        content = f.read()
        
        for sig, desc in signatures.items():
            positions = []
            start = 0
            while True:
                pos = content.find(sig, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
                if len(positions) >= 10:  # 限制找到的数量
                    break
            
            if positions:
                found_signatures.append((sig, desc, positions))
    
    return found_signatures

def extract_pyinstaller_info(filename):
    """尝试提取PyInstaller相关信息"""
    print("正在搜索PyInstaller特征...")
    
    with open(filename, 'rb') as f:
        content = f.read()
        
        # 查找PyInstaller版本信息
        version_patterns = [
            rb'PyInstaller: (\d+\.\d+\.\d+)',
            rb'pyinstaller==(\d+\.\d+\.\d+)',
            rb'PyInstaller (\d+\.\d+\.\d+)'
        ]
        
        for pattern in version_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"发现PyInstaller版本: {matches[0].decode()}")
        
        # 查找Python版本
        python_version_patterns = [
            rb'Python (\d+\.\d+\.\d+)',
            rb'python(\d+)(\d+)',
            rb'(\d+\.\d+) \(default'
        ]
        
        for pattern in python_version_patterns:
            matches = re.findall(pattern, content)
            if matches:
                if len(matches[0]) == 2:  # python38 格式
                    version = f"{matches[0][0].decode()}.{matches[0][1].decode()}"
                else:
                    version = matches[0].decode()
                print(f"发现Python版本: {version}")
                break
        
        # 查找导入的模块
        import_patterns = [
            rb'import ([a-zA-Z_][a-zA-Z0-9_]*)',
            rb'from ([a-zA-Z_][a-zA-Z0-9_]*) import'
        ]
        
        modules = set()
        for pattern in import_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                try:
                    module_name = match.decode('ascii')
                    if len(module_name) > 2 and module_name.isalpha():
                        modules.add(module_name)
                except:
                    pass
        
        if modules:
            print(f"\n发现的Python模块 (前20个):")
            for i, module in enumerate(sorted(modules)[:20]):
                print(f"  {i+1:2d}. {module}")
        
        # 查找可能的主程序文件名
        py_file_patterns = [
            rb'([a-zA-Z_][a-zA-Z0-9_]*\.py)',
            rb'__main__\.py',
            rb'main\.py'
        ]
        
        py_files = set()
        for pattern in py_file_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                try:
                    filename = match.decode('ascii')
                    if len(filename) > 3:
                        py_files.add(filename)
                except:
                    pass
        
        if py_files:
            print(f"\n发现的Python文件:")
            for py_file in sorted(py_files)[:10]:
                print(f"  - {py_file}")

def search_for_source_code(filename):
    """搜索可能的源代码片段"""
    print("\n正在搜索可能的源代码片段...")
    
    with open(filename, 'rb') as f:
        content = f.read()
        
        # 查找Python代码模式
        code_patterns = [
            rb'def [a-zA-Z_][a-zA-Z0-9_]*\([^)]*\):',
            rb'class [a-zA-Z_][a-zA-Z0-9_]*[^:]*:',
            rb'if __name__ == ["\']__main__["\']:',
            rb'import [a-zA-Z_][a-zA-Z0-9_]*',
            rb'from [a-zA-Z_][a-zA-Z0-9_]* import',
            rb'print\([^)]*\)',
            rb'return [^\\n\\r]*',
            rb'self\.[a-zA-Z_][a-zA-Z0-9_]*'
        ]
        
        found_code = []
        for pattern in code_patterns:
            matches = re.findall(pattern, content)
            for match in matches[:5]:  # 每种模式最多5个
                try:
                    code = match.decode('ascii', errors='ignore')
                    if len(code) > 5 and len(code) < 100:
                        found_code.append(code)
                except:
                    pass
        
        if found_code:
            print("发现的代码片段:")
            for i, code in enumerate(found_code[:15]):
                print(f"  {i+1:2d}. {code}")

def analyze_strings_for_functionality(filename):
    """分析字符串以推断软件功能"""
    print("\n正在分析软件功能...")
    
    with open(filename, 'rb') as f:
        content = f.read()
        
        # 功能相关的关键词
        functionality_keywords = {
            'GUI': ['window', 'button', 'dialog', 'menu', 'tkinter', 'qt', 'wx', 'gui'],
            '网络': ['http', 'https', 'url', 'request', 'socket', 'server', 'client', 'api'],
            '文件操作': ['file', 'path', 'directory', 'folder', 'read', 'write', 'save', 'load'],
            '系统操作': ['system', 'process', 'registry', 'service', 'admin', 'privilege'],
            '加密/安全': ['encrypt', 'decrypt', 'hash', 'password', 'key', 'crypto', 'ssl'],
            '数据库': ['database', 'sql', 'mysql', 'sqlite', 'mongodb', 'db'],
            '配置': ['config', 'setting', 'option', 'preference', 'ini', 'json', 'xml'],
            '日志': ['log', 'debug', 'info', 'warn', 'error', 'trace'],
            '更新': ['update', 'version', 'download', 'install', 'upgrade'],
            '自动化': ['auto', 'schedule', 'task', 'job', 'cron', 'timer']
        }
        
        # 提取所有可读字符串
        strings = re.findall(rb'[\x20-\x7E]{4,}', content)
        all_text = b' '.join(strings).lower()
        
        detected_functions = {}
        for category, keywords in functionality_keywords.items():
            count = 0
            found_keywords = []
            for keyword in keywords:
                keyword_count = all_text.count(keyword.encode())
                if keyword_count > 0:
                    count += keyword_count
                    found_keywords.append(f"{keyword}({keyword_count})")
            
            if count > 0:
                detected_functions[category] = (count, found_keywords)
        
        if detected_functions:
            print("检测到的功能模块:")
            for category, (count, keywords) in sorted(detected_functions.items(), key=lambda x: x[1][0], reverse=True):
                print(f"  {category}: {count} 次匹配")
                print(f"    关键词: {', '.join(keywords[:5])}")

def main():
    filename = "无线续杯Win系统v2.2.3版本.exe"
    
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return
    
    print(f"Python打包文件分析: {filename}")
    print("=" * 70)
    
    # 查找Python特征
    signatures = find_python_signatures(filename)
    if signatures:
        print("发现的Python特征:")
        for sig, desc, positions in signatures:
            print(f"  {desc}: {len(positions)} 处")
            if len(positions) <= 3:
                for pos in positions:
                    print(f"    位置: 0x{pos:08X}")
        print()
    
    # 提取PyInstaller信息
    extract_pyinstaller_info(filename)
    
    # 搜索源代码片段
    search_for_source_code(filename)
    
    # 分析功能
    analyze_strings_for_functionality(filename)

if __name__ == "__main__":
    main()
