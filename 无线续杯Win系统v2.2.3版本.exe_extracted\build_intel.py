#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
build_intel.py - 使用PyInstaller构建Intel版本的macOS应用程序
在Apple芯片Mac上构建Intel版本，并支持测试运行
"""

import os
import sys
import shutil
import platform
import subprocess
import venv
import time
from pathlib import Path

# 定义应用程序信息
APP_NAME = "无限续杯-Intel"
APP_VERSION = "2.2.2"
MAIN_SCRIPT = "ui.py"
ICON_FILE = "logo.icns"

# 虚拟环境设置
VENV_DIR = os.path.join(os.getcwd(), "venv_intel")
PYTHON_EXECUTABLE = os.path.join(VENV_DIR, "bin", "python")

# 确保图标文件存在
if not os.path.exists(ICON_FILE):
    print(f"错误: 图标文件 {ICON_FILE} 不存在!")
    sys.exit(1)

# 创建临时目录
BUILD_DIR = os.path.join(os.getcwd(), "build")
DIST_DIR = os.path.join(os.getcwd(), "dist")
SPEC_FILE = os.path.join(os.getcwd(), f"{APP_NAME}.spec")

def create_intel_venv():
    """创建Intel架构的Python虚拟环境"""
    print("正在创建Intel架构的Python虚拟环境...")
    
    # 检查是否在Apple Silicon上运行
    is_apple_silicon = False
    try:
        arch = subprocess.check_output(["uname", "-m"]).decode().strip()
        is_apple_silicon = arch == "arm64"
    except Exception:
        pass
    
    if is_apple_silicon:
        print("检测到Apple Silicon芯片，将创建x86_64架构的虚拟环境")
    else:
        print("检测到Intel芯片，将创建原生虚拟环境")
    
    # 如果虚拟环境已存在，先删除
    if os.path.exists(VENV_DIR):
        print(f"删除已存在的虚拟环境: {VENV_DIR}")
        shutil.rmtree(VENV_DIR)
    
    # 创建虚拟环境
    try:
        if is_apple_silicon:
            # 使用arch命令强制x86_64架构
            cmd = ["arch", "-x86_64", sys.executable, "-m", "venv", VENV_DIR]
            subprocess.run(cmd, check=True)
        else:
            # 直接创建虚拟环境
            venv.create(VENV_DIR, with_pip=True)
        
        print(f"成功创建虚拟环境: {VENV_DIR}")
        
        # 安装必要的包
        pip_cmd = [os.path.join(VENV_DIR, "bin", "pip")]
        
        print("正在安装必要的包...")
        subprocess.run(pip_cmd + ["install", "--upgrade", "pip"], check=True)
        subprocess.run(pip_cmd + ["install", "pyinstaller"], check=True)
        
        # 确保安装x86_64架构的包
        print("安装x86_64架构的PyQt6和Pillow...")
        # 先卸载可能存在的arm64版本
        subprocess.run(pip_cmd + ["uninstall", "-y", "PyQt6", "PyQt6-Qt6", "PyQt6-sip", "pillow"], check=False)
        
        # 安装x86_64架构的包
        subprocess.run(["arch", "-x86_64"] + pip_cmd + ["install", "--no-cache-dir", "pillow"], check=True)
        
        # 安装PyQt6，添加重试和超时选项
        for attempt in range(3):  # 尝试3次
            try:
                print(f"尝试安装PyQt6 (尝试 {attempt+1}/3)...")
                result = subprocess.run(
                    ["arch", "-x86_64"] + pip_cmd + [
                        "install", 
                        "--no-cache-dir", 
                        "--timeout", "120",
                        "--retries", "5",
                        "PyQt6"
                    ], 
                    check=False
                )
                if result.returncode == 0:
                    print("PyQt6安装成功!")
                    break
            except Exception as e:
                print(f"安装PyQt6失败: {e}")
            
            print("安装失败，稍后重试...")
            time.sleep(3)  # 等待3秒后重试
        else:
            print("警告: 无法安装PyQt6，将尝试继续构建...")
        
        # 安装项目依赖（如果有requirements.txt）
        if os.path.exists("requirements.txt"):
            print("安装项目依赖（除PyQt6和Pillow外）...")
            # 创建临时requirements文件，排除PyQt6和Pillow
            with open("requirements.txt", "r") as f:
                reqs = f.readlines()
            
            temp_reqs = []
            for req in reqs:
                if not req.lower().startswith(("pyqt6", "pillow")):
                    temp_reqs.append(req)
            
            if temp_reqs:
                with open("temp_requirements.txt", "w") as f:
                    f.writelines(temp_reqs)
                
                subprocess.run(["arch", "-x86_64"] + pip_cmd + ["install", "-r", "temp_requirements.txt"], check=True)
                os.remove("temp_requirements.txt")
        
        # 安装psutil（确保是x86_64架构）
        print("安装x86_64架构的psutil...")
        subprocess.run(["arch", "-x86_64"] + pip_cmd + ["install", "--no-cache-dir", "psutil"], check=True)
        
        return True
    except Exception as e:
        print(f"创建虚拟环境失败: {e}")
        return False

def test_run_app():
    """在虚拟环境中测试运行应用"""
    if not os.path.exists(VENV_DIR):
        print("错误: 虚拟环境不存在，请先创建虚拟环境")
        return False
    
    print(f"在Intel虚拟环境中测试运行应用: {MAIN_SCRIPT}")
    try:
        # 使用虚拟环境中的Python运行主脚本
        cmd = [PYTHON_EXECUTABLE, MAIN_SCRIPT]
        subprocess.run(cmd)
        return True
    except Exception as e:
        print(f"测试运行失败: {e}")
        return False

# 清理旧的构建文件
for dir_path in [BUILD_DIR, DIST_DIR]:
    if os.path.exists(dir_path):
        print(f"清理目录: {dir_path}")
        shutil.rmtree(dir_path)

if os.path.exists(SPEC_FILE):
    print(f"删除旧的spec文件: {SPEC_FILE}")
    os.remove(SPEC_FILE)

# 准备PyInstaller命令
def prepare_pyinstaller_command():
    """准备PyInstaller命令行参数"""
    # 使用虚拟环境中的pyinstaller
    pyinstaller_path = os.path.join(VENV_DIR, "bin", "pyinstaller")
    
    cmd = [
        pyinstaller_path,
        "--name", APP_NAME,
        "--windowed",  # 无控制台窗口
        "--clean",     # 清理临时文件
        "--noconfirm", # 不询问确认
        "--add-data", f"{ICON_FILE}{os.pathsep}image",  # 添加图标文件
    ]
    
    # 在macOS上，使用onedir模式可以更好地处理权限问题
    if platform.system() == "Darwin":
        cmd.append("--onedir")   # 目录模式（macOS）
        cmd.extend(["--osx-bundle-identifier", "com.cursor.free.vip"])  # 添加Bundle ID
        
        # 强制使用Intel架构
        cmd.extend(["--target-architecture", "x86_64"])
    else:
        cmd.append("--onefile")   # 单文件模式（仅Windows和Linux）
    
    # 添加所有Python文件和资源文件
    python_files = [f for f in os.listdir() if f.endswith('.py') and f != MAIN_SCRIPT and f != 'build_app.py']
    for py_file in python_files:
        cmd.extend(["--add-data", f"{py_file}{os.pathsep}."])
    
    # 确保image目录完整打包
    if os.path.isdir("image"):
        cmd.extend(["--add-data", f"image{os.pathsep}image"])
    
    # 添加其他可能的资源目录
    for dir_name in ["resources", "drivers"]:
        if os.path.isdir(dir_name):
            cmd.extend(["--add-data", f"{dir_name}{os.pathsep}{dir_name}"])
    
    # 添加图标
    if platform.system() == "Windows":
        # 在Windows上，需要.ico文件
        ico_file = convert_to_ico()
        if ico_file:
            cmd.extend(["--icon", ico_file])
    else:
        # 在macOS上，可以直接使用.png或.icns
        cmd.extend(["--icon", ICON_FILE])
    
    # 添加主脚本
    cmd.append(MAIN_SCRIPT)
    
    return cmd

def convert_to_ico():
    """将PNG转换为ICO文件 (仅Windows)"""
    if platform.system() != "Windows":
        return None
        
    try:
        from PIL import Image
        ico_path = os.path.join(os.getcwd(), "logo.ico")
        
        # 打开PNG图像
        img = Image.open(ICON_FILE)
        
        # 保存为ICO
        img.save(ico_path, format='ICO')
        print(f"已创建ICO文件: {ico_path}")
        return ico_path
    except Exception as e:
        print(f"警告: 无法转换图标为ICO格式: {e}")
        print("将使用原始PNG图标")
        return ICON_FILE

def create_dmg():
    """为macOS创建Intel版本的DMG文件"""
    print("开始创建Intel版本的DMG安装文件...")
    
    try:
        app_path = os.path.join(DIST_DIR, f"{APP_NAME}.app")
        dmg_path = os.path.join(DIST_DIR, f"{APP_NAME}-{APP_VERSION}.dmg")
        
        # 检查.app文件是否存在
        if not os.path.exists(app_path):
            print(f"错误: 找不到.app文件: {app_path}")
            return
        
        # 设置正确的文件权限
        print("正在设置应用程序权限...")
        subprocess.run(["chmod", "-R", "755", app_path])
        
        # 创建一个临时目录用于DMG内容
        dmg_temp_dir = os.path.join(BUILD_DIR, "dmg_temp")
        if os.path.exists(dmg_temp_dir):
            shutil.rmtree(dmg_temp_dir)
        os.makedirs(dmg_temp_dir, exist_ok=True)
        
        # 复制.app到临时目录（使用ditto保留权限和属性）
        print(f"正在复制应用到临时目录: {dmg_temp_dir}")
        subprocess.run(["ditto", app_path, os.path.join(dmg_temp_dir, f"{APP_NAME}.app")])
        
        # 创建Applications文件夹的符号链接
        print("创建Applications文件夹的别名...")
        applications_symlink = os.path.join(dmg_temp_dir, "Applications")
        if os.path.exists(applications_symlink):
            os.remove(applications_symlink)
        os.symlink("/Applications", applications_symlink)
        
        # 创建使用说明文件
        readme_path = os.path.join(dmg_temp_dir, "使用说明.txt")
        with open(readme_path, "w", encoding="utf-8") as f:
            f.write(f"""Mac系统运行说明（Intel版本）：

3. 首次运行时，Mac可能会识别为恶意软件并阻止运行
4. 解决方法：打开系统偏好设置 -> 隐私与安全性 -> 下拉找到被阻止的应用 -> 点击"允许"按钮
5. 如果应用崩溃，请先右键点击应用，选择"显示包内容"，然后进入Contents/MacOS目录，双击可执行文件运行
6. 安装方法：将左侧应用拖动到右侧Applications文件夹即可完成安装

祝您使用愉快！
""")
        
        print("正在创建DMG文件...")
        
        # 方法1: 使用hdiutil (macOS自带)
        cmd = [
            "hdiutil", "create",
            "-volname", APP_NAME,
            "-srcfolder", dmg_temp_dir,
            "-ov", "-format", "UDZO",
            dmg_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"成功创建DMG文件: {dmg_path}")
            # 验证DMG文件
            print("正在验证DMG文件...")
            verify_result = subprocess.run(["hdiutil", "verify", dmg_path], capture_output=True, text=True)
            if verify_result.returncode == 0:
                print("DMG文件验证成功")
            else:
                print(f"DMG文件验证失败: {verify_result.stderr}")
        else:
            print(f"创建DMG文件失败: {result.stderr}")
            
            # 备选方法: 使用create-dmg工具
            print("尝试使用create-dmg工具...")
            alt_cmd = [
                "create-dmg",
                "--volname", APP_NAME,
                "--window-pos", "200", "100",
                "--window-size", "800", "500",
                "--icon-size", "100",
                "--icon", f"{APP_NAME}.app", "200", "200",
                "--hide-extension", f"{APP_NAME}.app",
                "--app-drop-link", "600", "200",
                dmg_path,
                dmg_temp_dir
            ]
            
            try:
                result = subprocess.run(alt_cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"成功创建DMG文件: {dmg_path}")
                else:
                    print(f"使用create-dmg创建DMG文件失败: {result.stderr}")
            except Exception as e:
                print(f"执行create-dmg时出错: {e}")
                
        # 清理临时目录
        shutil.rmtree(dmg_temp_dir, ignore_errors=True)
    
    except Exception as e:
        print(f"创建DMG文件时出错: {e}")

def main():
    """主函数"""
    # 检查是否在macOS上运行
    if platform.system() != "Darwin":
        print("错误: 此脚本仅支持在macOS上构建Intel版本的应用")
        sys.exit(1)
    
    # 创建Intel架构的虚拟环境
    print("步骤1: 创建Intel架构的Python虚拟环境")
    if not create_intel_venv():
        print("错误: 无法创建Intel虚拟环境，构建终止")
        sys.exit(1)
    
    # 询问用户是否要先测试运行应用
    test_first = input("是否要先在Intel环境中测试运行应用? (y/n): ").lower() == 'y'
    if test_first:
        if not test_run_app():
            continue_build = input("测试运行可能有问题，是否继续构建? (y/n): ").lower() == 'y'
            if not continue_build:
                print("构建已取消")
                sys.exit(0)
    
    print("步骤2: 准备构建Intel版本的应用")
    
    # 准备并执行PyInstaller命令
    cmd = prepare_pyinstaller_command()
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 检查是否在Apple Silicon上运行
        is_apple_silicon = False
        try:
            arch = subprocess.check_output(["uname", "-m"]).decode().strip()
            is_apple_silicon = arch == "arm64"
        except Exception:
            pass
        
        if is_apple_silicon:
            print("在Apple Silicon上构建Intel应用，使用arch -x86_64运行PyInstaller")
            # 使用arch命令强制在x86_64架构下运行
            arch_cmd = ["arch", "-x86_64"] + cmd
            result = subprocess.run(arch_cmd, capture_output=True, text=True)
        else:
            # 在Intel芯片上直接运行
            result = subprocess.run(cmd, capture_output=True, text=True)
        
        # 输出构建日志
        print("\n--- 构建日志 ---")
        print(result.stdout)
        
        if result.returncode != 0:
            print("\n--- 错误日志 ---")
            print(result.stderr)
            print(f"构建失败，错误代码: {result.returncode}")
            sys.exit(1)
        
        print(f"Intel版本应用程序构建成功!")
        
        # 在macOS上进行额外的应用程序验证和修复
        app_path = os.path.join(DIST_DIR, f"{APP_NAME}.app")
        print(f"\n正在验证和修复Intel版本macOS应用程序: {app_path}")
        
        # 确保所有资源文件被正确打包
        macos_bin = os.path.join(app_path, "Contents", "MacOS", APP_NAME)
        if os.path.exists(macos_bin):
            # 设置可执行权限
            subprocess.run(["chmod", "+x", macos_bin])
            print(f"已设置可执行权限: {macos_bin}")
            
            # 验证应用架构
            try:
                arch_result = subprocess.run(["file", macos_bin], capture_output=True, text=True)
                if "x86_64" in arch_result.stdout:
                    print("✅ 确认应用为Intel(x86_64)架构")
                else:
                    print(f"⚠️ 警告: 应用可能不是Intel架构: {arch_result.stdout.strip()}")
            except Exception as e:
                print(f"无法验证应用架构: {e}")
            
            # 使用codesign验证应用
            try:
                subprocess.run(["codesign", "--verify", "--verbose", app_path], check=False)
                print("应用程序签名验证完成")
            except Exception as e:
                print(f"应用程序签名验证警告 (这可能不影响功能): {e}")
            
            # 复制资源文件夹到正确位置
            resources_dir = os.path.join(app_path, "Contents", "Resources")
            for resource_dir in ["image", "drivers"]:
                if os.path.exists(resource_dir) and not os.path.exists(os.path.join(resources_dir, resource_dir)):
                    try:
                        dest_dir = os.path.join(resources_dir, resource_dir)
                        shutil.copytree(resource_dir, dest_dir)
                        print(f"已复制资源目录: {resource_dir} -> {dest_dir}")
                    except Exception as e:
                        print(f"复制资源目录时出错: {e}")
        
        # 询问用户是否要测试运行构建好的应用
        test_built = input("\n是否要测试运行构建好的Intel应用? (y/n): ").lower() == 'y'
        if test_built:
            print(f"正在启动构建好的Intel应用: {app_path}")
            try:
                subprocess.Popen(["open", app_path])
            except Exception as e:
                print(f"启动应用失败: {e}")
        
        # 创建DMG文件
        should_create_dmg = input("是否要创建DMG安装文件? (y/n): ").lower() == 'y'
        if should_create_dmg:
            create_dmg()
            
        # 此版本仅支持macOS，不再需要Windows相关代码
        
        print(f"\nIntel版本构建完成! 输出文件位于: {DIST_DIR}")
        print(f"应用程序路径: {os.path.join(DIST_DIR, f'{APP_NAME}.app')}")
        if os.path.exists(os.path.join(DIST_DIR, f"{APP_NAME}-{APP_VERSION}.dmg")):
            print(f"DMG安装文件: {os.path.join(DIST_DIR, f'{APP_NAME}-{APP_VERSION}.dmg')}")
        
    except Exception as e:
        print(f"构建过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 